<script setup lang="ts">
import type { Selectable } from 'kysely';

import { Icon } from '@iconify/vue';

import type { KyselyDatabase } from '@/database/kysely';

import BaseContentCard from './BaseContentCard.vue';

const props = defineProps({
  tierSet: {
    type: Object as () => Selectable<KyselyDatabase['tierSet']> & {
      _count: { tierLanes: number; tierItems: number };
    },
    required: true,
  },
});

const targetUrl = computed(() => `/tiers/${props.tierSet.slug}/`);
const lanesUrl = computed(() => `/tiers/${props.tierSet.slug}/lanes`);

const backgroundUrl = computed(() => {
  let url = `/og-image/tiers`;

  if (props.tierSet) {
    url += `/${props.tierSet.slug}`;
  }

  url += '.png';

  return url;
});

const laneCount = computed(() => props.tierSet._count?.tierLanes);
const itemCount = computed(() => props.tierSet._count?.tierItems);
</script>

<template>
  <BaseContentCard :url="targetUrl" :bg="backgroundUrl">
    <template #title>
      {{ props.tierSet.label ?? '' }}
    </template>
    <template #body>
      <div class="flex items-center justify-center gap-2 text-sm">
        <div v-if="laneCount" class="flex items-center gap-1">
          <Icon icon="mdi:order-alphabetical-ascending"></Icon>
          {{ Intl.NumberFormat().format(laneCount) }}
        </div>
        <div v-if="laneCount && itemCount">•</div>
        <div v-if="itemCount" class="flex items-center gap-1">
          <Icon icon="mdi:cards"></Icon>
          {{ Intl.NumberFormat().format(itemCount) }}
        </div>
      </div>
      <div>{{ props.tierSet.description ?? '' }}</div>
    </template>
    <!-- <template #action>
      <a
        :class="[
          'flex justify-center items-center p-1 gap-1 w-fit',
          'bg-transparent rounded-md outline outline-gray-100 outline-1',
          'hover:bg-white hover:text-black',
        ]"
        :href="lanesUrl"
      >
        <Icon icon="mdi:cards-variant" :inline="true"></Icon>
        <span>View Lanes</span>
      </a>
    </template> -->
  </BaseContentCard>
</template>
