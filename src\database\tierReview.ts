import { format } from 'date-fns';
import { th } from 'date-fns/locale';

import { calculateFinalScore } from '@/helpers/scores';

export type ComputedPropertiesOfTierReview = {
  reviewTitle?: string;
  reviewContent?: string;
  scoreBias?: number;
  scoreCreative?: number;
  scoreFun?: number;
  scorePotential?: number;
  scoreGraphic?: number;
  scoreUi?: number;
  scoreAudio?: number;
  scoreControl?: number;
  scoreStability?: number;
  scoreBuy?: number;
  publishDate?: Date;
  publishDateFormatted?: string;
  finalScore?: number;
};

export function applyComputedTierReview<T extends object>(input: T) {
  const applied: Omit<T, 'publishDate'> & ComputedPropertiesOfTierReview = input as any;

  if ('publishDate' in applied) {
    if (typeof applied.publishDate === 'string') {
      applied.publishDate = new Date(applied.publishDate);
    }
  }

  applied.reviewTitle = applied.reviewTitle || '';

  applied.reviewContent = applied.reviewContent || '';

  applied.publishDateFormatted = (() => {
    return applied.publishDate ? format(applied.publishDate, 'dd MMMM yyyy', { locale: th }) : '';
  })();

  applied.finalScore = calculateFinalScore(applied);

  return applied;
}
