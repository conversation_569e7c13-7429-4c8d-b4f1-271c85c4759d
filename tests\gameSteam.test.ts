import { describe, expect, it } from 'vitest';

import { filterSchema } from '../src/server/routers/gameSteam';

describe('GameSteam Router', () => {
  describe('filterSchema', () => {
    it('should accept REVIEW_SCORE as a valid sort value', () => {
      const input = {
        sorts: [
          {
            value: 'REVIEW_SCORE',
            order: 1,
          },
        ],
      };

      const result = filterSchema.safeParse(input);
      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data.sorts[0].value).toBe('REVIEW_SCORE');
        expect(result.data.sorts[0].order).toBe(1);
      }
    });

    it('should accept REVIEW_SCORE with descending order', () => {
      const input = {
        sorts: [
          {
            value: 'REVIEW_SCORE',
            order: -1,
          },
        ],
      };

      const result = filterSchema.safeParse(input);
      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data.sorts[0].value).toBe('REVIEW_SCORE');
        expect(result.data.sorts[0].order).toBe(-1);
      }
    });

    it('should reject invalid sort values', () => {
      const input = {
        sorts: [
          {
            value: 'INVALID_SORT',
            order: 1,
          },
        ],
      };

      const result = filterSchema.safeParse(input);
      expect(result.success).toBe(false);
    });

    it('should accept multiple sorts including REVIEW_SCORE', () => {
      const input = {
        sorts: [
          {
            value: 'REVIEW_SCORE',
            order: -1,
          },
          {
            value: 'GAME:gameName',
            order: 1,
          },
        ],
      };

      const result = filterSchema.safeParse(input);
      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data.sorts).toHaveLength(2);
        expect(result.data.sorts[0].value).toBe('REVIEW_SCORE');
        expect(result.data.sorts[1].value).toBe('GAME:gameName');
      }
    });
  });
});
