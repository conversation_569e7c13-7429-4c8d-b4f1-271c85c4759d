import * as _ from 'lodash-es';
import { defineStore } from 'pinia';
import { type DataTableOperatorFilterMetaData, type DataTableSortMeta } from 'primevue/datatable';

import { FilterMatchMode, FilterOperator } from '@primevue/core/api';

import type { KyselyEnums } from '@/database/kysely';
import type { TierItemForMainPage } from '@/database/tierItem';
import type { TierSetForMainPage } from '@/database/tierSet';
import { trpc } from '@/services/trpc';

export { pinia } from './';

export type GameSteamWithRating = Awaited<
  ReturnType<typeof trpc.gameSteam.list.query>
>['data'][number] & {
  rating: { [tierSetSlug: string]: true };
  galleriaItems: {
    name?: string;
    img?: string;
    teaser?: string;
    video?: string;
  }[];
};

export const useItemStore = defineStore('item', () => {
  const schema = ref<TierSetForMainPage[]>([]);
  const itemInfoMap = ref<Record<number, TierItemForMainPage>>({});
  const clonedIdList = ref<number[]>([]);
  const gameSteamOnTierSetMap = ref<Record<number, string[]>>({});
  // Client Filters
  const fShowingItemTypes = ref<KyselyEnums['TierItemType'][]>(['NORMAL', 'GAME_STEAM']);
  const fSearchText = ref('');
  const hasDemo = ref<true | false | null>(null);
  const hasReview = ref<true | false | null>(null);
  const filters = ref<{ [key: string]: DataTableOperatorFilterMetaData }>({
    tags: {
      operator: FilterOperator.AND,
      constraints: [{ value: [], matchMode: FilterMatchMode.CONTAINS }],
    },
    event: {
      operator: FilterOperator.AND,
      constraints: [{ value: [], matchMode: FilterMatchMode.CONTAINS }],
    },
    hypeness: {
      operator: FilterOperator.AND,
      constraints: [{ value: [], matchMode: FilterMatchMode.CONTAINS }],
    },
    suckz: {
      operator: FilterOperator.AND,
      constraints: [{ value: [], matchMode: FilterMatchMode.CONTAINS }],
    },
  });
  const sortingFields = ref<(DataTableSortMeta & { field: string })[]>([
    { field: 'hypeness', order: 1 },
    { field: 'event', order: 1 },
  ]);
  // Displaying game model state
  const displayingTierItem = ref<TierItemForMainPage>();
  const canEditDisplayingTierItem = ref(false);
  const focusedGame = ref<GameSteamWithRating | null>(null);

  function refreshgameSteamOnTierSetMap() {
    const map: Record<number, string[]> = {};
    Object.values(itemInfoMap.value).forEach(item => {
      const steamId = (item?.gameSteam as any)?.steamId ?? item?.gameSteamId;
      if (steamId && item.tierSetSlug) {
        map[steamId] = [...(map[steamId] ?? []), item.tierSetSlug];
      }
    });
    gameSteamOnTierSetMap.value = map;
  }

  function assignItemInfoByObject(val: Record<number, Pick<TierItemForMainPage, 'id'> | null>) {
    itemInfoMap.value = {
      ...(itemInfoMap.value as any),
      ...val,
    };
    refreshgameSteamOnTierSetMap();
  }
  function assignItemInfoByList(val: Pick<TierItemForMainPage, 'id'>[]) {
    itemInfoMap.value = {
      ...itemInfoMap.value,
      ..._.keyBy(val, 'id'),
    };
    refreshgameSteamOnTierSetMap();
  }

  return {
    schema,
    itemInfoMap,
    clonedIdList,
    gameSteamOnTierSetMap,
    fShowingItemTypes,
    fSearchText,
    hasDemo,
    hasReview,
    filters,
    sortingFields,
    displayingTierItem,
    canEditDisplayingTierItem,
    focusedGame,
    assignItemInfoByObject,
    assignItemInfoByList,
  };
});
