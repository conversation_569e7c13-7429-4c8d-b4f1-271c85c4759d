import type { ColumnType } from 'kysely';

export type Generated<T> =
  T extends ColumnType<infer S, infer I, infer U>
    ? ColumnType<S, I | undefined, U>
    : ColumnType<T, T | undefined, T>;
export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export const TierItemType = {
  NORMAL: 'NORMAL',
  GAME_STEAM: 'GAME_STEAM',
  FROM_LANE: 'FROM_LANE',
} as const;
export type TierItemType = (typeof TierItemType)[keyof typeof TierItemType];
export const PermissionType = {
  PRIVATE: 'PRIVATE',
  PUBLIC: 'PUBLIC',
} as const;
export type PermissionType = (typeof PermissionType)[keyof typeof PermissionType];
export type GameSteam = {
  id: Generated<number>;
  gameName: Generated<string>;
  gameNameEn: Generated<string>;
  gameUrl: Generated<string>;
  steamId: number;
  slug: Generated<string>;
  gameTags: Generated<string[]>;
  recordedVideo: Generated<string[]>;
  nominations: Generated<string[]>;
  awards: Generated<string[]>;
  releaseDate: Timestamp | null;
  shortDescription: Generated<string>;
  detailedDescription: Generated<string>;
  aboutTheGame: Generated<string>;
  website: Generated<string>;
  publishers: Generated<string[]>;
  developers: Generated<string[]>;
  movies: Generated<unknown[]>;
  screenshots: Generated<unknown[]>;
  hasAgeCheck: Generated<boolean>;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  hasDemo: Generated<boolean>;
  isAvailable: Generated<boolean>;
  hasImageBroadcastLeftPanel: Generated<boolean>;
  hasImageBroadcastRightPanel: Generated<boolean>;
  hasImageCapsuleLg: Generated<boolean>;
  hasImageCapsuleSm: Generated<boolean>;
  hasImageHeader: Generated<boolean>;
  hasImageHeroCapsule: Generated<boolean>;
  hasImageLibraryHero: Generated<boolean>;
  hasImageLibraryPoster: Generated<boolean>;
  hasImageLogo: Generated<boolean>;
  hasImagePageBgBlur: Generated<boolean>;
  hasImagePageBgRaw: Generated<boolean>;
  demoId: number | null;
};
export type Player = {
  id: Generated<number>;
  displayName: Generated<string>;
  userId: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type PlayersOnSpaces = {
  playerId: number;
  spaceId: number;
  assignedBy: string | null;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type Space = {
  id: Generated<number>;
  slug: string;
  label: Generated<string>;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type TierItem = {
  id: Generated<number>;
  gameSteamId: number | null;
  tierLaneSlug: string | null;
  lexorank: string | null;
  note: Generated<string>;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  reviewContent: Generated<string>;
  reviewTitle: Generated<string>;
  tierSetSlug: string;
  publishDate: Timestamp | null;
  displayUrls: Generated<string[]>;
  targetUrl: Generated<string>;
  type: Generated<TierItemType>;
  fromLaneSlug: string | null;
  fromLaneTierSetSlug: string | null;
  clonedFromTierItemId: number | null;
  tsv: Generated<string>;
};
export type TierLane = {
  id: Generated<number>;
  slug: string;
  label: Generated<string>;
  icon: Generated<string>;
  mainColor: Generated<string>;
  textColor: Generated<string>;
  description: Generated<string>;
  score: Generated<number>;
  lexorank: string | null;
  tierSetSlug: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
};
export type TierSet = {
  id: Generated<number>;
  slug: string;
  createdAt: Generated<Timestamp>;
  updatedAt: Generated<Timestamp>;
  description: Generated<string>;
  icon: Generated<string>;
  label: Generated<string>;
  mainColor: Generated<string>;
  textColor: Generated<string>;
  spaceId: number | null;
  permissionType: Generated<PermissionType>;
};
export type DB = {
  gameSteam: GameSteam;
  player: Player;
  playersOnSpaces: PlayersOnSpaces;
  space: Space;
  tierItem: TierItem;
  tierLane: TierLane;
  tierSet: TierSet;
};
