<script setup lang="ts">
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import type { SortableEvent, SortableOptions } from 'sortablejs';
import type { AutoScrollOptions } from 'sortablejs/plugins';
import Draggable from 'vuedraggable';

import { Icon } from '@iconify/vue';

import type { KyselyEnums } from '@/database/kysely';
import type { TierLaneForMainPage, TierSetForMainPage } from '@/database/tierSet';
import { getLexorankBetween } from '@/helpers/utils';
import { trpc } from '@/services/trpc';
import { pinia, useItemStore } from '@/stores/item';
import { useUserStore } from '@/stores/user';

import TierLane from './TierLane.vue';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const toast = useToast();

const userStore = useUserStore();
const { space } = storeToRefs(userStore);

const itemStore = useItemStore(pinia);
const { schema } = storeToRefs(itemStore);

const tierSet = computed({
  get() {
    return schema.value[props.index];
  },
  set(val) {
    schema.value[props.index] = val;
  },
});
provide('tierSet', tierSet);

const parentForChildren = computed({
  get() {
    return tierSet.value.tierLanes;
  },
  set(val) {
    tierSet.value.tierLanes = val;
  },
});
provide('parent', parentForChildren);

const isLoading = ref(false);

const setDataToUpdate = reactive({
  label: tierSet.value.label ?? '',
  slug: tierSet.value.slug ?? '',
  permissionType: tierSet.value.permissionType ?? ('PUBLIC' as KyselyEnums['PermissionType']),
});
const isEditingTitle = ref(false);

const connectedToDb = ref(true);
provide('connectedToDb', connectedToDb);
const canEdit = computed(() => !connectedToDb.value || tierSet.value.canEdit);

async function applyUpdate() {
  isLoading.value = true;
  try {
    if (unref(connectedToDb)) {
      const updated = await trpc.tierSet.update.mutate({
        tierSetSlug: tierSet.value.slug,
        ...setDataToUpdate,
      });

      if (!updated) {
        throw new Error('Failed to update Tierset');
      }

      if (tierSet.value.slug === updated.slug) {
        tierSet.value = {
          ...tierSet.value,
          label: updated.label,
          slug: updated.slug,
        };
      } else {
        window.location.replace(`/tiers/${updated.slug}`);
      }
    }
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't update Tierset",
      detail: 'Maybe the something is already taken?, Or just the internet mystery',
      life: 20000,
    });
  }

  isLoading.value = false;
}

async function callUpdateLaneApi(lane: Partial<TierSetForMainPage>) {
  const updated = await trpc.tierLane.update.mutate({
    tierSetSlug: tierSet.value.slug,
    tierLaneSlug: lane.slug!,
    ...(lane as any),
  });

  return updated;
}

function onUpdate(e: SortableEvent): void {
  if (e.oldIndex === undefined || e.newIndex === undefined) return;

  const item = tierSet.value.tierLanes[e.oldIndex];
  const prev = tierSet.value.tierLanes[e.newIndex - 1];
  const next = tierSet.value.tierLanes[e.newIndex + 1];
  item.lexorank = getLexorankBetween(prev?.lexorank, next?.lexorank);

  tierSet.value.tierLanes = [
    ...tierSet.value.tierLanes.slice(0, e.oldIndex),
    ...tierSet.value.tierLanes.slice(e.oldIndex + 1),
  ] as typeof tierSet.value.tierLanes;
  tierSet.value.tierLanes = [
    ...tierSet.value.tierLanes.slice(0, e.newIndex),
    item,
    ...tierSet.value.tierLanes.slice(e.newIndex),
  ] as typeof tierSet.value.tierLanes;

  if (unref(connectedToDb)) {
    callUpdateLaneApi(item);
  }

  console.log('Lane Moved:', tierSet.value.slug, { e, item, prev, next });
}

async function addLane() {
  isLoading.value = true;
  try {
    const displayName = `${tierSet.value.slug}-${tierSet.value.tierLanes.length}`;
    const lexorank = getLexorankBetween(tierSet.value.tierLanes.at(-1)?.lexorank, null);
    let tierLane: TierLaneForMainPage = {} as TierLaneForMainPage;
    if (unref(connectedToDb)) {
      tierLane = (await trpc.tierLane.create.mutate({
        tierSetSlug: tierSet.value.slug,
        displayName,
        lexorank,
      })) as unknown as TierLaneForMainPage;
    } else {
      tierLane.slug = displayName;
      tierLane.label = displayName;
      tierLane.lexorank = lexorank;
      tierLane.mainColor = '#000000';
      tierLane.textColor = '#ffffff';
      tierLane.tierItems = [];
    }
    tierSet.value.tierLanes = [...tierSet.value.tierLanes, tierLane];
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't add the lane",
      detail: 'Maybe internet just has a hiccup',
      life: 20000,
    });
  }
  isLoading.value = false;
}

async function deleteLane(index: number) {
  isLoading.value = true;
  try {
    const lane = tierSet.value.tierLanes[index];
    if (unref(connectedToDb)) {
      await trpc.tierLane.delete.mutate({
        tierSetSlug: tierSet.value.slug,
        tierLaneSlug: lane.slug,
      });
    }
    tierSet.value.tierLanes = [
      ...tierSet.value.tierLanes.slice(0, index),
      ...tierSet.value.tierLanes.slice(index + 1),
    ] as typeof tierSet.value.tierLanes;
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't delete the lane",
      detail: "Maybe it's chosen by a cosmic being",
      life: 20000,
    });
  }
  isLoading.value = false;
}

function togglePlayTierset() {
  connectedToDb.value = !connectedToDb.value;
}

async function copyTierset() {
  isLoading.value = true;
  const newTierSet = await trpc.tierSet.copy.mutate({
    spaceId: space.value?.id!,
    tierSetSlug: tierSet.value.slug,
  });
  if (newTierSet) {
    window.location.href = `/tiers/${newTierSet.slug}/`;
  }
  isLoading.value = false;
}

const confirm = useConfirm();
async function deleteTierset() {
  async function accept() {
    isLoading.value = true;
    await trpc.tierSet.delete.mutate({
      tierSetSlug: tierSet.value.slug,
    });
    isLoading.value = false;
    window.location.href = '/tiers';
  }

  confirm.require({
    message: `Are you sure you want to delete "${tierSet.value.label}" ?`,
    header: 'Hold !!!',
    accept,
    reject: () => {
      //
    },
  });
}

async function confirmUpdateTitle() {
  await applyUpdate();
  isEditingTitle.value = false;
}

function cancelUpdateTitle() {
  setDataToUpdate.label = tierSet.value.label ?? '';
  isEditingTitle.value = false;
}

const options = computed<SortableOptions | AutoScrollOptions>(() => {
  return {
    group: `tiers-${tierSet.value.slug}`,
    draggable: '.draggable',
    handle: '.handle',
    filter: '.ignore-drag',
    preventOnFilter: false,
    delay: 300,
    delayOnTouchOnly: true,
    animation: 150,
    ghostClass: 'ghost',
    dragClass: 'drag',
    forceFallback: true,
    scroll: true,
    bubbleScroll: true,
    disabled: !canEdit.value,
  };
});
</script>

<template>
  <div class="mb-2">
    <!-- Showing -->
    <div v-if="!isEditingTitle" class="group/title flex items-center justify-start">
      <h2 class="p-1 indent-4 text-base font-black">{{ setDataToUpdate.label }}</h2>
      <div
        v-if="canEdit"
        :class="[
          'cursor-pointer text-sm text-(--text)',
          'opacity-0 transition-opacity group-hover/title:opacity-70',
        ]"
        @click.stop="isEditingTitle = true"
      >
        <Icon icon="lucide:settings" />
      </div>
    </div>
    <!-- Editing -->
    <div v-else class="ignore-drag flex items-center justify-start gap-1 p-1 text-xs">
      <div class="relative">
        <Icon icon="lucide:text" class="absolute my-2 mr-2 ml-2 text-xs text-(--main)" />
        <PrimeInputText
          v-model="setDataToUpdate.label"
          inputId="tier-set-label"
          :placeholder="tierSet.label ?? ''"
          :class="[
            'bg-(--text)! py-1! pl-6! text-xs! text-(--main)!',
            'placeholder:text-(--main)! placeholder:opacity-30',
          ]"
        />
        <Icon icon="lucide:code" class="absolute my-2 mr-2 ml-2 text-xs text-(--main)" />
        <PrimeInputText
          v-model="setDataToUpdate.slug"
          inputId="tier-set-slug"
          :placeholder="tierSet.slug ?? ''"
          :class="[
            'bg-(--text)! py-1! pl-6! text-xs! text-(--main)!',
            'placeholder:text-(--main)! placeholder:opacity-30',
          ]"
        />
        <PrimeSelectButton
          v-model="setDataToUpdate.permissionType"
          :options="[{ value: 'PRIVATE' }, { value: 'PUBLIC' }]"
          :allowEmpty="false"
          optionLabel="value"
          optionValue="value"
        >
        </PrimeSelectButton>
      </div>

      <PrimeButton
        class="text-theme-text! bg-green-600! text-xs!"
        @click="confirmUpdateTitle"
        :loading="isLoading"
      >
        <Icon icon="lucide:save" />
      </PrimeButton>
      <PrimeButton class="bg-gray-600! text-xs! text-gray-200!" @click="cancelUpdateTitle">
        <Icon icon="mdi:cancel" />
      </PrimeButton>
      <PrimeButton
        :class="[
          'bg-transparent! text-xs! text-red-500! outline-1! outline-red-600!',
          'hover:animate-wiggle-more hover:animate-duration-75 hover:animate-infinite hover:bg-red-200!',
        ]"
        @click="deleteTierset"
        :loading="isLoading"
      >
        <Icon icon="lucide:trash" />
      </PrimeButton>
      <PrimeConfirmDialog>
        <template #icon>
          <Icon icon="lucide:trash" class="mr-2 inline"></Icon>
        </template>
      </PrimeConfirmDialog>
    </div>
  </div>
  <Draggable
    v-model="tierSet.tierLanes"
    v-bind="options"
    item-key="slug"
    tag="div"
    class="flex flex-col items-center gap-0.5"
    @update="onUpdate"
  >
    <template #item="{ element: lane, index: i }: { element: TierSetForMainPage; index: number }">
      <TierLane
        :index="i"
        :key="lane.slug"
        :can-add="canEdit && connectedToDb && i === tierSet.tierLanes.length - 1"
        :can-edit="canEdit"
        class="draggable"
        @delete:lane="deleteLane(i)"
      />
    </template>
    <template #footer> </template>
  </Draggable>
  <div class="my-2 flex justify-between">
    <div class="flex gap-1">
      <PrimeButton v-if="canEdit" class="" :disabled="isLoading" @click="addLane">
        <Icon icon="lucide:plus" class="mr-1" />
        <span>Add Lane</span>
      </PrimeButton>
    </div>
    <div class="flex gap-1">
      <PrimeButton
        v-if="connectedToDb"
        class="bg-yellow-600!"
        :disabled="isLoading"
        @click="togglePlayTierset"
      >
        <Icon icon="lucide:monitor-play" class="mr-1" />
        <span>Play without save</span>
      </PrimeButton>
      <PrimeButton
        v-if="connectedToDb && space"
        class="bg-yellow-600!"
        :disabled="isLoading"
        @click="copyTierset"
      >
        <Icon icon="lucide:panel-right-inactive" class="mr-1" />
        <span>Copy to my space</span>
      </PrimeButton>
    </div>
  </div>
</template>
