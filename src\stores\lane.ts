import * as _ from 'lodash-es';
import { defineStore } from 'pinia';

import type { TierItemForMainPage } from '@/database/tierItem';

export { pinia } from './';

export const useLaneStore = defineStore('lane', () => {
  const laneTierItems = ref<Record<string, TierItemForMainPage[]>>({});

  function assignLaneTierItems(val: Record<string, TierItemForMainPage[]>) {
    const cleaned: typeof val = {};
    _.forEach(val, (items, key) => {
      const clonedIds = items.map(item => item.clonedFromTierItemId).filter(id => !!id);
      cleaned[key] = [];
      items.forEach(item => {
        // cleaned[key].push(item);
        if (item.type === 'FROM_LANE') {
          // cleaned[key].push(
          //   ...((item.fromLane?.tierItems as unknown as TierItemForMainPage[]) ?? []),
          // );

          const filteredItems = item.fromLane.tierItems.filter(
            fItem => !clonedIds.includes(fItem.id),
          );
          cleaned[key].push({
            ...item,
            fromLane: {
              ...item.fromLane,
              tierItems: filteredItems as any,
            },
          });
        } else {
          cleaned[key].push(item);
        }
      });
    });
    laneTierItems.value = {
      ...laneTierItems.value,
      ...cleaned,
    };
  }

  return { laneTierItems, assignLaneTierItems };
});
