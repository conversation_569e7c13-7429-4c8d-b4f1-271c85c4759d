import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresQueryCompiler } from 'kysely';
import * as _ from 'lodash-es';
import kyselyExtension from 'prisma-extension-kysely';

import { PermissionType, Prisma, PrismaClient } from '@prisma/client';

import { mergeCustomizer } from '@/helpers/utils';
import { SERVER_ENV } from '@/server/env';

import type { DB } from '../../prisma/generated/types';

function createPrismaClient() {
  const logOptions = [
    ...(SERVER_ENV.DEV
      ? ([
          // {
          //   emit: 'event',
          //   level: 'query',
          // },
          // {
          //   emit: 'stdout',
          //   level: 'info',
          // },
          {
            emit: 'stdout',
            level: 'warn',
          },
        ] as const)
      : []),
    {
      emit: 'stdout',
      level: 'error',
    },
  ] satisfies Prisma.PrismaClientOptions['log'];
  const client = new PrismaClient({
    log: logOptions,
  });

  // client.$on('query', e => {
  //   console.log(`[${e.timestamp.toISOString()}][${e.duration}ms] ${e.query} (params: ${e.params})`);
  //   console.log(`-------------------------`);
  // });

  return client.$extends(kysely()).$extends(computedProperties()).$extends(helperMethods());
}

const prismaGlobal = global as typeof global & {
  prisma?: ReturnType<typeof createPrismaClient>;
};

export const prisma = prismaGlobal.prisma ?? createPrismaClient();

if (process.env.NODE_ENV !== 'production') {
  prismaGlobal.prisma = prisma;
}

function bypassRLS() {
  return Prisma.defineExtension(prisma =>
    prisma.$extends({
      query: {
        $allModels: {
          async $allOperations({ args, query }) {
            const [, result] = await prisma.$transaction([
              prisma.$executeRaw`SELECT set_config('app.bypass_rls', 'on', TRUE)`,
              query(args),
            ]);
            return result;
          },
        },
      },
    }),
  );
}

function fromUser(userId: string) {
  return Prisma.defineExtension(prisma =>
    prisma.$extends({
      name: `fromUser: ${userId}`,
      query: {
        $allModels: {
          async $allOperations({ args, query }) {
            const [, result] = await prisma.$transaction([
              prisma.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`,
              query(args),
            ]);
            return result;
          },
        },
        tierItem: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            const newArgs = _.mergeWith(
              args,
              {
                where: {
                  tierSet: {
                    space: {
                      playersOnSpaces: {
                        some: { player: { userId: { in: userId ? [userId] : [] } } },
                      },
                    },
                  },
                },
              } satisfies typeof args,
              mergeCustomizer,
            );
            return query(newArgs);
          },
        },
        tierLane: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            return query(
              _.mergeWith(
                args,
                {
                  where: {
                    tierSet: {
                      space: {
                        playersOnSpaces: {
                          some: { player: { userId: { in: userId ? [userId] : [] } } },
                        },
                      },
                    },
                  },
                } as typeof args,
                mergeCustomizer,
              ),
            );
          },
        },
        tierSet: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            return query(
              _.mergeWith(
                args,
                {
                  where: {
                    space: {
                      playersOnSpaces: {
                        some: { player: { userId: { in: userId ? [userId] : [] } } },
                      },
                    },
                  },
                } as typeof args,
                mergeCustomizer,
              ),
            );
          },
        },
      },
    }),
  );
}

function toUser(userId?: string) {
  return Prisma.defineExtension(prisma =>
    prisma.$extends({
      name: `toUser: ${userId}`,
      query: {
        tierItem: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            const newArgs = _.mergeWith(
              args,
              {
                where: {
                  AND: [
                    {
                      OR: [
                        { tierSet: { permissionType: PermissionType.PUBLIC } },
                        {
                          tierSet: {
                            permissionType: PermissionType.PRIVATE,
                            space: {
                              playersOnSpaces: {
                                some: { player: { userId: { in: userId ? [userId] : [] } } },
                              },
                            },
                          },
                        },
                      ],
                    },
                  ],
                },
              } satisfies typeof args,
              mergeCustomizer,
            );
            // console.log(JSON.stringify(newArgs, null, 2));
            return query(newArgs);
          },
        },
        tierLane: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            return query(
              _.mergeWith(
                args,
                {
                  where: {
                    AND: [
                      {
                        OR: [
                          { tierSet: { permissionType: PermissionType.PUBLIC } },
                          {
                            tierSet: {
                              permissionType: PermissionType.PRIVATE,
                              space: {
                                playersOnSpaces: {
                                  some: { player: { userId: { in: userId ? [userId] : [] } } },
                                },
                              },
                            },
                          },
                        ],
                      },
                    ],
                  },
                } as typeof args,
                mergeCustomizer,
              ),
            );
          },
        },
        tierSet: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            return query(
              _.mergeWith(
                args,
                {
                  where: {
                    AND: [
                      {
                        OR: [
                          { permissionType: PermissionType.PUBLIC },
                          {
                            permissionType: PermissionType.PRIVATE,
                            space: {
                              playersOnSpaces: {
                                some: { player: { userId: { in: userId ? [userId] : [] } } },
                              },
                            },
                          },
                        ],
                      },
                    ],
                  },
                } as typeof args,
                mergeCustomizer,
              ),
            );
          },
        },
        space: {
          $allOperations({ args, query }) {
            if ('data' in args) {
              // Bypass 'create'
              return query(args);
            }
            return query(
              _.mergeWith(
                args,
                {
                  include: {
                    tierSets: {
                      where: {
                        AND: [
                          {
                            OR: [
                              { permissionType: PermissionType.PUBLIC },
                              {
                                permissionType: PermissionType.PRIVATE,
                                space: {
                                  playersOnSpaces: {
                                    some: { player: { userId: { in: userId ? [userId] : [] } } },
                                  },
                                },
                              },
                            ],
                          },
                        ],
                      },
                    },
                  },
                } as typeof args,
                mergeCustomizer,
              ),
            );
          },
        },
      },
      result: {
        tierSet: {
          canEdit: {
            needs: { spaceId: true },
            compute(tierSet) {
              if (!userId) {
                return false;
              }
              type TierSet = Prisma.TierSetGetPayload<{
                include: {
                  space: { include: { playersOnSpaces: { include: { player: true } } } };
                };
              }>;
              return !!(tierSet as TierSet).space?.playersOnSpaces?.find(
                ps => ps.player?.userId === userId,
              );
            },
          },
        },
        space: {
          canEdit: {
            needs: {},
            compute(space) {
              if (!userId) {
                return false;
              }
              type Space = Prisma.SpaceGetPayload<{
                include: { playersOnSpaces: { select: { player: true } } };
              }>;
              return !!(space as Space).playersOnSpaces?.find(ps => ps.player?.userId === userId);
            },
          },
        },
      },
    }),
  );
}

function kysely() {
  return kyselyExtension({
    kysely: driver =>
      new Kysely<DB>({
        dialect: {
          // This is where the magic happens!
          createDriver: () => driver,
          // Don't forget to customize these to match your database!
          createAdapter: () => new PostgresAdapter(),
          createIntrospector: db => new PostgresIntrospector(db),
          createQueryCompiler: () => new PostgresQueryCompiler(),
        },
        plugins: [],
      }),
  });
}

function computedProperties() {
  return Prisma.defineExtension(prisma =>
    prisma.$extends({
      name: `computedProperties`,
    }),
  );
}

function helperMethods() {
  return Prisma.defineExtension(prisma =>
    prisma.$extends({
      name: `helperMethods`,
      client: {
        $fromUser: (userId: string) => {
          return prisma.$extends(fromUser(userId));
        },
        $toUser: (userId?: string) => {
          return prisma.$extends(toUser(userId));
        },
        $fromToUser: (fromUserId: string, toUserId?: string) => {
          return prisma.$extends(fromUser(fromUserId)).$extends(toUser(toUserId ?? fromUserId));
        },
      },
    }),
  );
}
