import type { AstroCookies } from 'astro';

import type { FetchCreateContextFnOptions } from '@trpc/server/adapters/fetch';

import { getSupabaseServerUser } from '@/database/supabase.server';

export function getCreateContextFunction({ cookies }: { cookies: AstroCookies }) {
  return async function createContext({ req, resHeaders }: FetchCreateContextFnOptions) {
    const user = await getSupabaseServerUser(req?.headers?.get('Cookie'), cookies);
    return { req, resHeaders, cookies, user };
  };
}

export type Context = Awaited<ReturnType<ReturnType<typeof getCreateContextFunction>>>;
