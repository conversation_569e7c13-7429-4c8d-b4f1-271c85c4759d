import { createRouter } from '../trpc';
import { blogRouter } from './blog';
import { craftRouter } from './craft';
import { filterPresetRouter } from './filterPreset';
import { gameSteamRouter } from './gameSteam';
import { simpleTierRouter } from './simpleTier';
import { spaceRouter } from './space';
import { tierItemRouter } from './tierItem';
import { tierLaneRouter } from './tierLane';
import { tierReviewRouter } from './tierReview';
import { tierSetRouter } from './tierSet';
import { userRouter } from './user';

export const appRouter = createRouter({
  blog: blogRouter,
  user: userRouter,
  space: spaceRouter,
  tierSet: tierSetRouter,
  tierLane: tierLaneRouter,
  tierItem: tierItemRouter,
  tierReview: tierReviewRouter,
  simpleTier: simpleTierRouter,
  gameSteam: gameSteamRouter,
  filterPreset: filterPresetRouter,
  craft: craftRouter,
});
// export type definition of API
export type AppRouter = typeof appRouter;
