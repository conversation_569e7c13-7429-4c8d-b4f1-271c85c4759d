---
import RankingBox from '@/components/RankingBox.vue';
import { SCORE_DEFINITIONS } from '@/helpers/scores';
import Layout from '@/layouts/Layout.astro';
import { getServerTrpcCaller } from '@/server/caller';
import type { BlogGameResult } from '@/server/routers/blog';

export const prerender = true;

const { caller } = await getServerTrpcCaller(Astro, { prerender: true });
const { reviewedGames } = await caller.blog.fetchGames();

// Helper function to get current year
const CURRENT_YEAR = 2025;
const currentYearGames = reviewedGames.filter(game => {
  const publishDate = new Date(game.tierReview?.createdAt || 0);
  return publishDate.getFullYear() === CURRENT_YEAR;
});

// Award categories configuration
let awardCategories: {
  items: BlogGameResult[];
  title: string;
  emoji: string;
  scoreColor: string;
  getScoreFunction: (games: BlogGameResult) => number | null | undefined;
}[] = [
  {
    title: `Best Demo`,
    emoji: '🏆',
    items: currentYearGames,
    scoreColor: '#0a0',
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Online Multiplayer`,
    emoji: '🌐',
    scoreColor: '#0aa',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['multiplayer', 'online', 'co-op', 'pvp'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Horror`,
    emoji: '👻',
    scoreColor: '#ff2a73',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems?.map(item => item.tierLaneSlug?.toLowerCase() || '').join(',');
      return ['steam-horror', 'steam-psychological-horror'].every(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Sandbox`,
    emoji: '📦',
    scoreColor: '#ac9178',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['sandbox'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Deckbuilding`,
    emoji: '📦',
    scoreColor: '#ac9178',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['deckbuilding'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Bullet Heaven`,
    emoji: '🔫',
    scoreColor: '#f43379',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['bullet hell', 'roguelike', 'survival'].every(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Roguelike`,
    emoji: '📦',
    scoreColor: '#ac9178',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['roguelike'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Simulation`,
    emoji: '📊',
    scoreColor: '#747949',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['simulation'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Strategy`,
    emoji: '🧠',
    scoreColor: '#5f5e6e',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['strategy'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Puzzle`,
    emoji: '🧩',
    scoreColor: '#428723',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['puzzle'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Wholesome`,
    emoji: '❤️',
    scoreColor: '#e43e3e',
    items: currentYearGames.filter(game => {
      const tagStr = game.tierItems
        ?.map(item => item.tierLane?.label?.toLowerCase() || '')
        .join(',');
      return ['wholesome', 'cozy'].some(tag => tagStr.includes(tag));
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Best Graphics`,
    emoji: '🎨',
    scoreColor: SCORE_DEFINITIONS.scoreGraphic.color,
    items: currentYearGames,
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.scoreGraphic,
  },
  {
    title: `Best Audio`,
    emoji: '🎵',
    scoreColor: SCORE_DEFINITIONS.scoreAudio.color,
    items: currentYearGames,
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.scoreAudio,
  },
  {
    title: `Most Creative`,
    emoji: '💡',
    scoreColor: SCORE_DEFINITIONS.scoreCreative.color,
    items: currentYearGames,
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.scoreCreative,
  },
  {
    title: `Most Fun`,
    emoji: '🎉',
    scoreColor: SCORE_DEFINITIONS.scoreFun.color,
    items: currentYearGames,
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.scoreFun,
  },
  {
    title: `Best Free`,
    emoji: '💰',
    scoreColor: SCORE_DEFINITIONS.scoreBuy.color,
    items: currentYearGames.filter(game => game.tierReview?.scoreBuy! === 100),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
  {
    title: `Hidden Gems`,
    emoji: '💎',
    scoreColor: '#296bcc',
    items: currentYearGames.filter(game => {
      const finalScore = game.tierReview?.finalScore || 0;
      const biasScore = game.tierReview?.scoreBias || 0;
      return finalScore >= 70 && biasScore <= 60; // Good games with low bias (underrated)
    }),
    getScoreFunction: (game: BlogGameResult) => game.tierReview?.finalScore,
  },
];

awardCategories = awardCategories.map(c => ({
  ...c,
  title: (c.title += ` ${CURRENT_YEAR}`),
  items: c.items
    .map(game => ({ ...game, score: c.getScoreFunction(game) as number }))
    .filter(game => typeof game.score === 'number')
    .toSorted((a, b) => b.score! - a.score!),
}));
---

<Layout title="Game Rankings" showSearch={false}>
  <section class="px-2">
    <div class="mb-8 text-center">
      <h1 class="title mb-4 text-3xl">🏆 The Demoman Rankings {CURRENT_YEAR}</h1>
      <p class="mx-auto max-w-2xl text-gray-400">
        เดโมเกมที่ดีที่สุดแห่งปี ในหมวดต่างๆ อ้างอิงจากคะแนน ณ ช่วงนั้นๆ ที่เล่น<br />
        วิธีจัดอันดับ และคะแนนที่แสดง ต่างกันไปตามเกณฑ์ของหมวดนั้นๆ<br />
      </p>
      <div class="mt-4">
        <a
          href="/"
          class="text-theme-primary hover:text-theme-secondary font-medium transition-colors"
        >
          ← Back to Home
        </a>
      </div>
    </div>

    <!-- Awards Grid -->
    <div class="awards-container">
      {
        awardCategories.map(c => (
          <RankingBox
            items={c.items}
            title={c.title}
            emoji={c.emoji}
            scoreColor={c.scoreColor}
            compact
            client:load
          />
        ))
      }
    </div>

    <!-- Footer Info -->
    <div class="mt-12 text-center text-sm text-gray-500">
      <p>ทุกอย่างตัดสินจากเหตุผลและความชอบส่วนตัวล้วนๆ</p>
      <p>อาจจะมีเกมที่ท่านไม่รู้จัก แต่ก็หวังว่าจะได้เกมใหม่ๆ ไปเล่นกันนะครับ :)</p>
    </div>
  </section>
</Layout>

<style>
  .awards-container {
    display: grid;
    gap: 2rem;
    max-width: 4xl;
    margin: 0 auto;
  }

  @media (min-width: 768px) {
    .awards-container {
      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    }
  }
</style>
