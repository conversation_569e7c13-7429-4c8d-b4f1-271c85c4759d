<script setup lang="ts">
import { format } from 'date-fns';

import TagLink from '@/components/TagLink.vue';
import TierItemLinks from '@/components/TierItemLinks.vue';
import { cn } from '@/helpers/cn';
import { getScoreColor, getScoreTooltip } from '@/helpers/scores';
import { findValidImageUrl } from '@/helpers/utils';
import type { BlogGameResult } from '@/server/routers/blog';

const props = defineProps<{
  game: BlogGameResult;
}>();

const tierItemsToShow = computed(() =>
  [
    props.game?.tierItems?.find(ti => ti.tierLane?.tierSetSlug === 'hypeness')!,
    // props.game?.tierItems?.find(ti => ti.tierLane?.tierSetSlug === 'suckz' && ti.reviewTitle)! ||
    //   props.game?.tierItems?.find(ti => ti.tierLane?.tierSetSlug === 'suckz')!,
  ].filter(ti => !!ti),
);

const reviewContent = computed(() => props.game.tierReview?.reviewContent ?? '');

const finalImageUrl = computed(() => {
  return findValidImageUrl(props.game);
});

const createdAtFormatted = computed(() => {
  return format(props.game.tierReview?.createdAt!, 'dd/MM/yy');
});

// Score data
const finalScore = computed(() => props.game?.tierReview?.finalScore);
const scoreColor = computed(() =>
  typeof finalScore.value === 'number' ? getScoreColor(finalScore.value) : null,
);
</script>

<template>
  <div
    :class="
      cn(
        'relative block h-full w-full rounded-lg shadow-sm transition-all',
        'overflow-hidden whitespace-nowrap',
        'duration-200 ease-in-out hover:shadow-md',
        'dark:text-gray-200',
        'border border-gray-200/80 hover:border-gray-300 dark:border-gray-700/50 dark:hover:border-gray-600',
      )
    "
  >
    <div
      class="flex flex-row items-center gap-2 bg-size-[30%] bg-left bg-no-repeat px-1 py-1"
      :style="{
        backgroundImage: `linear-gradient(to right, rgba(0,0,0,0) 0%, var(--color-theme-bg) 90%), url(${finalImageUrl})`,
      }"
    >
      <!-- Main Title: Game Name + Review Title -->
      <div class="flex items-center gap-1 md:ml-24">
        <div
          class="max-w-[25vw] truncate font-bold text-gray-900 text-shadow-lg md:max-w-[200px] dark:text-white"
          v-tooltip.top="game.gameName"
        >
          {{ game.gameName ?? '' }}
        </div>
        <div
          v-if="reviewContent"
          class="w-full max-w-[25vw] truncate text-xs md:max-w-[200px]"
          v-tooltip.top="reviewContent"
        >
          {{ reviewContent }}
        </div>
      </div>
      <div class="absolute right-1 flex items-center gap-1">
        <TierItemLinks :tierItem="{ ...tierItemsToShow[0], gameSteam: game }" />
        <!-- Score Display -->
        <TagLink
          v-for="(review, i) in tierItemsToShow"
          :key="review.id"
          :tierSetSlug="review.tierLane!.tierSetSlug"
          :tierLane="review.tierLane!"
          :class="cn(i === 0 ? 'md:w-16' : 'md:w-22', 'h-3 w-4 md:h-full')"
        />
        <div
          v-if="typeof finalScore === 'number'"
          class="flex h-6 w-6 cursor-help items-center justify-center rounded-full text-xs font-bold shadow-sm transition-transform hover:scale-110"
          :style="`background-color: ${scoreColor}; color: white;`"
          v-tooltip.top="getScoreTooltip(props.game)"
        >
          {{ Math.round(finalScore) }}
        </div>

        <div class="font-mono text-xs">
          {{ createdAtFormatted }}
        </div>
      </div>
    </div>
  </div>
</template>
