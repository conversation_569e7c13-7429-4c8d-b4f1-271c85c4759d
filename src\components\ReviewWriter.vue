<script setup lang="ts">
import DatePicker from 'primevue/datepicker';
import { useToast } from 'primevue/usetoast';

import { Icon } from '@iconify/vue';
import { MilkdownProvider } from '@milkdown/vue';
import { useVModels } from '@vueuse/core';

import { trpc } from '@/services/trpc';
import { pinia, useUserStore } from '@/stores/user';

import MarkdownEditor from './MarkdownEditor.vue';

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
  reviewTitle: {
    type: String,
    required: true,
  },
  reviewContent: {
    type: String,
    required: true,
  },
  publishDate: {
    type: Date,
    required: true,
  },
});

const toast = useToast();

const emit = defineEmits(['update:tierItem']);

const { reviewTitle, reviewContent, publishDate, id } = useVModels(props, emit);

const userStore = useUserStore(pinia);
const { isDemomanSpace } = storeToRefs(userStore);

const isLoading = ref(false);

async function saveReview() {
  isLoading.value = true;
  try {
    await trpc.tierReview.createOrUpdate.mutate({
      gameSteamId: id.value,
      reviewTitle: reviewTitle.value,
      reviewContent: reviewContent.value,
      publishDate: publishDate.value ? publishDate.value.toISOString() : undefined,
    });
    toast.add({
      severity: 'success',
      summary: 'Updated',
      detail: reviewTitle.value,
      life: 5000,
    });
  } catch (err) {
    console.error(err);
    toast.add({
      severity: 'error',
      summary: "Can't update",
      detail: 'Sadly',
      life: 20000,
    });
  }
  isLoading.value = false;
}

watch(publishDate, () => {
  publishDate.value?.setHours(12);
});
</script>

<template>
  <div class="flex flex-col gap-1">
    <div class="flex gap-1">
      <div class="flex w-11/12 gap-1">
        <Icon icon="lucide:text" class="my-2 text-xs text-(--main)" />
        <PrimeInputText
          v-model="reviewTitle"
          :disabled="!isDemomanSpace"
          inputId="tier-item-label"
          :placeholder="reviewTitle"
          :class="[
            'h-full bg-(--text)! pl-2! text-(--main)!',
            'placeholder:text-(--main)! placeholder:opacity-30',
          ]"
        />
      </div>
      <div class="flex gap-1">
        <Icon icon="lucide:calendar" class="my-2 text-xs text-(--main)" />
        <DatePicker
          v-model="publishDate"
          :disabled="!isDemomanSpace"
          dateFormat="yy-mm-dd"
          inputId="publishDate"
          :class="[
            'h-full w-full bg-(--text)! text-(--main)!',
            'placeholder:text-(--main)! placeholder:opacity-30',
          ]"
        />
      </div>

      <PrimeButton
        class="text-theme-text! w-1/12! justify-center bg-green-600! text-xs!"
        @click="saveReview"
        :loading="isLoading"
      >
        <Icon icon="lucide:save" />
      </PrimeButton>
    </div>

    <MilkdownProvider>
      <MarkdownEditor v-model="reviewContent" :disabled="!isDemomanSpace" />
    </MilkdownProvider>
  </div>
</template>

<style scoped lang="scss"></style>
