import DOMPurify from 'isomorphic-dompurify';
import type { Compilable } from 'kysely';
import { LexoRank } from 'lexorank';
import { marked } from 'marked';
import slugify from 'slugify';

import type { BlogGameResult } from '@/server/routers/blog';

marked.use({ breaks: true, silent: true });

export function getLexorankBetween(left?: string | null, right?: string | null) {
  const prevStr = left ?? '';
  const nextStr = right ?? '';
  const prevLexorank = prevStr ? LexoRank.parse(prevStr) : LexoRank.min();
  let nextLexorank = nextStr ? LexoRank.parse(nextStr) : LexoRank.max();
  if (nextLexorank.equals(prevLexorank)) {
    nextLexorank = nextLexorank.genNext();
  }
  const curLexorank = prevLexorank.between(nextLexorank);
  const curStr = curLexorank.toString();
  return curStr;
}

export function generateLexoranks<T extends { lexorank: string | null }>(arr: T[]) {
  const filled = arr;
  arr.forEach((item, i) => {
    if (!item) {
      return null;
    }
    const prevStr = i > 0 ? filled[i - 1]?.lexorank : '';
    const nextStr = i < filled.length - 1 ? filled[i + 1]?.lexorank : '';
    const prevLexorank = prevStr ? LexoRank.parse(prevStr) : LexoRank.min();
    const nextLexorank = nextStr ? LexoRank.parse(nextStr) : LexoRank.max();
    const curLexorank = prevLexorank.between(nextLexorank);
    const curStr = curLexorank.toString();
    item.lexorank = curStr;
    filled[i] = item;
  });
  return filled;
}

export function sanitize(html: string, configs: Parameters<typeof DOMPurify.sanitize>[1] = {}) {
  if (!DOMPurify.isSupported) {
    return html;
  }
  const clean = DOMPurify.sanitize(html, configs);
  return clean.toString();
}

export function toMarkDown(text: string) {
  return marked.parse(text);
}

export function toggleClass(element: HTMLElement, className: string) {
  element.classList.toggle(className);
}

export function elementHasClass(element: HTMLElement, className: string) {
  return element.classList.contains(className);
}

export function getExtFromUrl(url: string) {
  return url.split('.').pop()!.split(/[#?]/)[0];
}

export function checkIfImageUrl(url: string) {
  return ['jpg', 'jpeg', 'png', 'bmp', 'mpeg', 'webp', 'gif'].includes(getExtFromUrl(url));
}

export function checkIfVideoUrl(url: string) {
  return ['mp4', 'webm'].includes(getExtFromUrl(url));
}

export function mergeCustomizer(objValue: any, srcValue: any) {
  if (Array.isArray(objValue)) {
    return objValue.concat(srcValue);
  }
}

export function slugifyWithSettings(text: string) {
  return slugify(text, {
    lower: true,
    strict: true,
    trim: true,
  });
}

export function filterOnlyReviewed<
  T extends { tierSetSlug?: string; reviewTitle?: string; publishDate?: string | Date }[],
>(entries: T) {
  return entries.filter(ti => ti?.tierSetSlug === 'suckz' && ti.publishDate && ti.reviewTitle) as T;
}

export function sortByPublishDate<T extends { publishDate?: string | Date }[]>(entries: T) {
  return entries.sort((a, b) => (a.publishDate! > b.publishDate! ? -1 : 1));
}

export function logQuery(query: Compilable) {
  const qToLog = query.compile();
  console.log(qToLog.query.kind, qToLog.sql, qToLog.parameters);
}

export function detectFileType(content: string) {
  if (content.startsWith('data:')) {
    return content.substring(content.indexOf(':') + 1, content.indexOf(';')).split('/')[1]; // => image/png
  }
  if (content.startsWith('http')) {
    return content.split(/[#?]/)[0].split('.').pop()?.trim() || false;
  }

  return false;
}

export function detectIfFileImage(content: string) {
  return ['jpg', 'jpeg', 'png', 'bmp', 'mpeg', 'webp', 'gif'].includes(
    detectFileType(content) || '',
  );
}

export function detectIfFileVideo(content: string) {
  return ['mp4', 'webm'].includes(detectFileType(content) || '');
}

function getImageUrls(game: Partial<BlogGameResult>) {
  return [
    ...(game?.screenshots?.map(ss => ss.path_full) || []),
    game?.imageFinalWide!,
    game?.imagePageBgBlur!,
    game?.imageFinalTall!,
    `https://placehold.co/400x300/222/FFF?text=${(game.gameName ?? '').split(' ').join('+')}`, // A final, reliable fallback
  ].filter(Boolean);
}

export function findValidImageUrl(game: Partial<BlogGameResult>) {
  const imageUrls = getImageUrls(game);

  const finalImageUrl = imageUrls[0];
  return finalImageUrl;
}

export async function findValidImageUrlByFetch(game: Partial<BlogGameResult>, ensureImage = false) {
  const imageUrls = getImageUrls(game);

  async function findFirstValid() {
    for (const url of imageUrls) {
      try {
        // Use a HEAD request for efficiency - we only need to know if the file exists
        const response = await fetch(url, { method: 'HEAD', signal: AbortSignal.timeout(2000) });
        if (response.ok) {
          return url; // Found a working URL, return it immediately
        }
      } catch (error) {
        // console.warn(`Image URL failed to load: ${url}`, error);
      }
    }
    return imageUrls.at(-1)!; // Return the last URL (your placeholder) if all else fail
  }

  const finalImageUrl = ensureImage ? await findFirstValid() : imageUrls[0];
  return finalImageUrl;
}
