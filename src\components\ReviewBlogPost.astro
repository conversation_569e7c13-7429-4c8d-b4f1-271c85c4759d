---
import TagLink from '@/components/TagLink.astro';
import { findValidImageUrlByFetch } from '@/helpers/utils';
import { getScoreColor } from '@/helpers/scores';
import type { BlogGameResult } from '@/server/routers/blog';
import { Image } from 'astro:assets';
import * as _ from 'lodash-es';

interface Props {
  game: BlogGameResult;
  ensureImage?: boolean;
}

const { game, ensureImage = false } = Astro.props;

const gameName = game?.gameName ?? '';
const tierItems = game.tierItems;

const tierItemsToShow = [
  // tierItems.find(ti => ti.tierLane?.tierSetSlug === 'event')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'hypeness')!,
  // tierItems.find(ti => ti.tierLane?.tierSetSlug === 'suckz')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'steam-tag')!,
].filter(ti => !!ti);

const reviewTitle = game.tierReview?.reviewTitle ?? '';
const reviewContent = game.tierReview?.reviewContent ?? '';
const targetUrl = (reviewContent ? `/blog/${game?.slug}` : game?.gameUrl) ?? '#';

const reviewDateStr = game.tierReview?.createdAt;

const finalImageUrl = await findValidImageUrlByFetch(game, ensureImage);

// Score data
const finalScore = game.tierReview?.finalScore;
const scoreColor = typeof finalScore === 'number' ? getScoreColor(finalScore) : null;
---

<div
  class="group relative block h-full w-full overflow-hidden rounded-xl bg-gray-900 shadow-lg transition-all duration-300 ease-in-out hover:scale-105"
>
  <Image
    src={finalImageUrl}
    alt={gameName}
    width={400}
    height={300}
    class="absolute inset-0 h-full w-full object-cover transition-opacity duration-300 ease-in-out group-hover:opacity-75"
  />

  <div class="absolute inset-0 bg-linear-to-t from-black/90 via-black/60 to-transparent"></div>

  <a
    href={targetUrl}
    rel="prefetch"
    class="absolute inset-0 z-10"
    aria-label={`View details for ${gameName} ${reviewTitle}`}
  >
  </a>

  <div class="relative flex h-full flex-col justify-end p-4 text-white">
    <!-- Score Display -->
    {
      typeof finalScore === 'number' && (
        <div
          class="absolute top-4 right-4 z-20 flex h-8 w-8 cursor-help items-center justify-center rounded-full text-sm font-bold shadow-lg transition-transform hover:scale-110"
          style={`background-color: ${scoreColor}; color: white; text-shadow: 0 1px 2px rgba(0,0,0,0.8);`}
          title={`Final Score: ${finalScore}/100\n
                Bias: ${game.tierReview?.scoreBias ?? 'N/A'}\n
                Creative: ${game.tierReview?.scoreCreative ?? 'N/A'}\n
                Fun: ${game.tierReview?.scoreFun ?? 'N/A'}\n
                Potential: ${game.tierReview?.scorePotential ?? 'N/A'}\n
                Graphic: ${game.tierReview?.scoreGraphic ?? 'N/A'}\n
                UI: ${game.tierReview?.scoreUi ?? 'N/A'}\n
                Audio: ${game.tierReview?.scoreAudio ?? 'N/A'}\n
                Control: ${game.tierReview?.scoreControl ?? 'N/A'}\n
                Stability: ${game.tierReview?.scoreStability ?? 'N/A'}\n
                Buy: ${game.tierReview?.scoreBuy ?? 'N/A'}`}
        >
          {finalScore}
        </div>
      )
    }
    <!-- Title Section -->
    <h3
      class="text-lg leading-tight font-bold tracking-tight [text-shadow:0_2px_4px_rgba(0,0,0,0.7)]"
    >
      <span>{gameName}</span>
      <span class="font-normal">{reviewTitle}</span>
    </h3>

    <!-- Review Quote & Date Section -->
    {
      reviewContent && (
        <div class="mt-1 h-8 items-center overflow-hidden text-gray-200/90 opacity-60 transition-all duration-300 [text-shadow:0_1px_3px_rgba(0,0,0,0.7)] group-hover:h-auto group-hover:opacity-100">
          <q class="line-clamp-2 italic">{reviewContent}</q>
        </div>
      )
    }

    <!-- Tags Section -->
    <div class="tags z-20 mt-1 flex flex-wrap items-center gap-0.5 leading-0">
      {
        tierItemsToShow.map(
          review =>
            review.tierLane && (
              <div>
                <TagLink tierSetSlug={review.tierLane.tierSetSlug} tierLane={review.tierLane} />
              </div>
            ),
        )
      }
    </div>

    <!-- Review Date -->
    {
      reviewDateStr && (
        <time
          datetime={reviewDateStr}
          class="absolute right-0 mr-2 text-xs whitespace-nowrap text-gray-200/90"
        >
          {new Date(reviewDateStr).toLocaleDateString()}
        </time>
      )
    }
  </div>
</div>
