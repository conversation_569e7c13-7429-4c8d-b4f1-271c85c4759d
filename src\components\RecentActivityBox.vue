<script setup lang="ts">
import VirtualScroller from 'primevue/virtualscroller';

import { cn } from '@/helpers/cn';
import type { BlogGameResult } from '@/server/routers/blog';

import RecentActivityRow from './RecentActivityRow.vue';

const props = defineProps<{
  items: BlogGameResult[];
  offset?: number;
}>();

const playedThisMonth = ref(0);
const playedThisYear = ref(0);
const playedTotal = ref(0);

const playedThisMonthPositive = ref(0);
const playedThisYearPositive = ref(0);
const playedTotalPositive = ref(0);

onMounted(() => {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const startOfYear = new Date(now.getFullYear(), 0, 1);

  playedThisMonth.value = props.items.filter(item => {
    return new Date(item.tierReview?.createdAt) >= startOfMonth;
  }).length;
  playedThisYear.value = props.items.filter(item => {
    return new Date(item.tierReview?.createdAt) >= startOfYear;
  }).length;

  playedTotal.value = props.items.length;

  playedThisMonthPositive.value = props.items.filter(item => {
    return (
      item.tierReview?.finalScore! > 50 && new Date(item.tierReview?.createdAt) >= startOfMonth
    );
  }).length;

  playedThisYearPositive.value = props.items.filter(item => {
    return item.tierReview?.finalScore! > 50 && new Date(item.tierReview?.createdAt) >= startOfYear;
  }).length;

  playedTotalPositive.value = props.items.filter(item => {
    return item.tierReview?.finalScore! > 50;
  }).length;
});
</script>

<template>
  <div>
    <div class="mt-2 flex gap-4 font-mono text-xs text-gray-600 dark:text-gray-400">
      <span class="font-bold">Played:</span>
      <span>{{ playedTotal }}</span>
      <span>This month: {{ playedThisMonth }}</span>
      <span>This year: {{ playedThisYear }}</span>
    </div>
    <div class="mb-2 flex gap-4 font-mono text-xs text-teal-700 dark:text-teal-500">
      <span class="font-bold">Good:&nbsp;&nbsp;</span>
      <span>{{ playedTotalPositive.toLocaleString() }}</span>
      <span>This month: {{ playedThisMonthPositive }}</span>
      <span>This year: {{ playedThisYearPositive }}</span>
    </div>
    <VirtualScroller
      :items="items.slice(props.offset ?? 0)"
      :itemSize="50"
      :class="cn('h-[200px]')"
    >
      <template v-slot:item="{ item }: { item: BlogGameResult }">
        <RecentActivityRow :game="item" class="h-[50px]" />
      </template>
    </VirtualScroller>
  </div>
</template>
