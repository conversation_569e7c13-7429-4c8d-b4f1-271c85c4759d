import Button from 'primevue/button';
import Calandar from 'primevue/calendar';
import Checkbox from 'primevue/checkbox';
import PrimeVue, { type PrimeVuePTOptions } from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import Dialog from 'primevue/dialog';
import Divider from 'primevue/divider';
import Dropdown from 'primevue/dropdown';
import Fieldset from 'primevue/fieldset';
import Image from 'primevue/image';
import InlineMessage from 'primevue/inlinemessage';
import InputText from 'primevue/inputtext';
import Listbox from 'primevue/listbox';
import Menu from 'primevue/menu';
import MultiSelect from 'primevue/multiselect';
import { usePassThrough } from 'primevue/passthrough';
import SelectButton from 'primevue/selectbutton';
import Textarea from 'primevue/textarea';
import Toast from 'primevue/toast';
import ToastService from 'primevue/toastservice';
import Tooltip from 'primevue/tooltip';
import type { App } from 'vue';

import Lara from '@primevue/themes/lara';

import CustomColorPicker from '@/components/primevue-custom/CustomColorPicker.vue';

const pt = usePassThrough(
  {
    inputtext: {
      root: {
        class: ['md:w-full! pl-10'],
      },
    },
    datepicker: {
      pcInputText: {
        root: ['pl-2'],
      },
    },
    textarea: {
      root: {
        class: ['md:w-full!'],
      },
    },
    button: {
      root: {
        class: [
          'bg-surface-200 hover:bg-surface-300 p-2 border-none transition-all dark:bg-surface-700 dark:hover:bg-surface-600 dark:text-white',
        ],
      },
    },
    multiselect: {
      filterIcon: {
        class: ['right-0 mr-3'],
      },
      item: {
        class: ['flex'],
      },
    },
    dialog: {
      root: {
        class: ['w-[98vw]! md:w-[75vw]!'],
      },
    },
  } as PrimeVuePTOptions,
  {
    mergeProps: true,
    mergeSections: true,
  },
);

export function initPrimeVue(app: App) {
  app.use(PrimeVue, {
    theme: {
      preset: Lara,
      options: {
        prefix: 'p',
        darkModeSelector: 'system',
        cssLayer: {
          name: 'primevue',
          order: 'base, primevue',
        },
      },
    },
    ripple: true,
    pt,
  });
  app.use(ConfirmationService);
  app.use(ToastService);
  app.directive('tooltip', Tooltip);
  app.component('PrimeCalendar', Calandar);
  app.component('PrimeDivider', Divider);
  app.component('PrimeInputText', InputText);
  app.component('PrimeTextarea', Textarea);
  app.component('PrimeSelectButton', SelectButton);
  app.component('PrimeDropdown', Dropdown);
  app.component('PrimeListbox', Listbox);
  app.component('PrimeMultiSelect', MultiSelect);
  app.component('PrimeImage', Image);
  app.component('PrimeButton', Button);
  app.component('PrimeMenu', Menu);
  app.component('PrimeDialog', Dialog);
  app.component('PrimeInlineMessage', InlineMessage);
  app.component('PrimeFieldset', Fieldset);
  app.component('PrimeCustomColorPicker', CustomColorPicker);
  app.component('PrimeCheckBox', Checkbox);
  app.component('PrimeToast', Toast);
}
