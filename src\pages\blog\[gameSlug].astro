---
import { sanitize, toMarkDown } from '@/helpers/utils';
import GameLayout from '@/layouts/GameLayout.astro';
import { getServerTrpcCaller } from '@/server/caller';
import GameScoreBreakdown from '@/components/GameScoreBreakdown.vue';

export const prerender = true;

export async function getStaticPaths() {
  const { caller } = await getServerTrpcCaller(Astro, { prerender });
  const { reviewedGames } = await caller.blog.fetchGames();

  return reviewedGames.map(game => {
    return {
      params: { gameSlug: `${game.slug}` },
      props: { game },
    };
  });
}

const { gameSlug } = Astro.params;
const game = Astro.props.game;

const { movies, screenshots, tierReview } = game;
const content =
  sanitize(await toMarkDown(tierReview?.reviewContent ?? '')) ||
  'ยังไม่ได้เขียนรีวิว (No review yet)';
---

<GameLayout game={game}>
  <h2 id="review" data-pagefind-weight="0.5">Review</h2>
  <article data-pagefind-weight="6" set:html={content} />
  <!-- <h2 id="steam-content" data-pagefind-weight="0.5">Steam Content</h2> -->
  <!-- <h3 id="about-the-game" data-pagefind-weight="0.5">About the game</h3>
  <p data-pagefind-weight="5" set:html={aboutTheGame} /> -->
  <!-- <h3 id="detailed-description">Detailed Description</h3> -->
  <!-- <p data-pagefind-weight="4" set:html={detailedDescription} /> -->

  <h3 id="score-breakdown" data-pagefind-weight="0.5">Scores</h3>
  <!-- Score Breakdown -->
  {
    game && (
      <div class="mb-12">
        <GameScoreBreakdown game={game} variant="full" client:load />
      </div>
    )
  }
  <h3 id="steam-page" data-pagefind-weight="0.5">Steam Page</h3>
  <a href={game.gameUrl}>{game.gameUrl}</a>

  <h3 id="medias" data-pagefind-weight="0.5">Medias</h3>
  <h4 id="videos" data-pagefind-weight="0.5">Videos</h4>

  {
    movies.map((mv, i) => (
      <>
        <h5 id={`video-${mv.id}`} data-pagefind-weight="0.5">
          {mv.name || `Trailer ${i + 1}`}
        </h5>
        <video src={mv.teaser} controls loop autoplay style="width: 100%; height: 100%">
          <track kind="captions" />
        </video>
        <video src={mv.webm.max} controls style="width: 100%; height: 100%">
          <track kind="captions" />
        </video>
      </>
    ))
  }
  <h4 id="images" data-pagefind-weight="0.5">Images</h4>
  <img src={game.imageFinalWide} alt="Hero" style="max-height: 200px;" />
  {game.imagePageBgBlur && <img src={game.imagePageBgBlur} alt="Header" class="image-bg" />}

  {screenshots.map(ss => <img src={ss.path_full} alt="Screenshot" style="max-height: 200px;" />)}
  <!-- <iframe title="Steam Page" src={game.gameUrl} width="100%" height="1080px" allowfullscreen
    ></iframe> -->
  <style>
    .image-bg {
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
      width: 100%;
      z-index: -1000;
      opacity: 0.3;
      margin: 0 !important;
      padding: 0;
    }

    /* Score animations */
    .score-circle {
      animation: scoreGlow 2s ease-in-out infinite alternate;
    }

    @keyframes scoreGlow {
      0% {
        box-shadow: 0 0 20px rgba(var(--score-color-rgb), 0.3);
      }
      100% {
        box-shadow: 0 0 40px rgba(var(--score-color-rgb), 0.6);
      }
    }

    .score-bar {
      animation: fillBar 1.5s ease-out;
    }

    @keyframes fillBar {
      0% {
        width: 0%;
      }
    }

    /* Hover effects for score breakdown */
    .score-item:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease-in-out;
    }

    .score-item:hover .score-bar {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
  </style>
</GameLayout>
