import type { Selectable } from 'kysely';

import type { ComputedPropertiesOfGameSteam } from '@/database/gameSteam';
import type { KyselyDatabase } from '@/database/kysely';

// Score definitions with labels and colors
export const SCORE_DEFINITIONS = {
  scoreBias: {
    key: 'scoreBias',
    label: 'Bias',
    abbr: 'Bias',
    desc: 'ความรู้สึกส่วนตัว',
    icon: '⚖️',
    color: '#EF4444',
    weight: 0.2,
  },
  scoreCreative: {
    key: 'scoreCreative',
    label: 'Creative',
    abbr: 'Crea',
    desc: 'ความสร้างสรรค์ แปลกใหม่ แหวกแนว',
    icon: '🎨',
    color: '#F59E0B',
    weight: 0.15,
  },
  scoreFun: {
    key: 'scoreFun',
    label: 'Fun',
    abbr: 'Fun',
    desc: 'ความสนุก ตื่นเต้น ในมุมคนคนทั่วไป',
    icon: '😄',
    color: '#EC4899',
    weight: 0.15,
  },
  scorePotential: {
    key: 'scorePotential',
    label: 'Potential',
    abbr: 'Potn',
    desc: 'ความเป็นไปได้ที่จะพัฒนาได้เรื่อยๆ',
    icon: '🚀',
    color: '#84CC16',
    weight: 0.15,
  },
  scoreBuy: {
    key: 'scoreBuy',
    label: 'Buy',
    abbr: 'Buy',
    desc: 'ความอยากซื้อ ความรู้สึกคุ้มค่า ไม่ผิดหวัง',
    icon: '💰',
    color: '#10B981',
    weight: 0.1,
  },
  scoreGraphic: {
    key: 'scoreGraphic',
    label: 'Graphic',
    abbr: 'Grap',
    desc: 'ภาพกราฟิก โมเดล เอฟเฟค',
    icon: '🖼️',
    color: '#FB5CF6',
    weight: 0.05,
  },
  scoreUi: {
    key: 'scoreUi',
    label: 'UI/UX',
    abbr: 'UI/UX',
    desc: 'ความใช้ง่าย ความสวยของ Interface',
    icon: '📱',
    color: '#8B5CF6',
    weight: 0.05,
  },
  scoreAudio: {
    key: 'scoreAudio',
    label: 'Audio',
    abbr: 'Snd',
    desc: 'ดนตรี เสียงเอฟเฟค',
    icon: '🎵',
    color: '#06B6D4',
    weight: 0.05,
  },
  scoreControl: {
    key: 'scoreControl',
    label: 'Control',
    abbr: 'Ctrl',
    desc: 'การควบคุม ความเข้าใจง่าย ใช้ง่าย',
    icon: '🎮',
    color: '#3B82F6',
    weight: 0.05,
  },
  scoreStability: {
    key: 'scoreStability',
    label: 'Stability',
    abbr: 'Stab',
    desc: 'ความเสถียร ไม่บัค ไม่กระตุก ไม่หลุด',
    icon: '🛡️',
    color: '#6B7280',
    weight: 0.05,
  },
} as const;

export const getScoreTooltip = (
  game: ComputedPropertiesOfGameSteam,
) => `Final Score: ${Math.round(game?.tierReview?.finalScore || 0)}/100
            
                Bias: ${game?.tierReview?.scoreBias ?? 'N/A'}
                Creative: ${game?.tierReview?.scoreCreative ?? 'N/A'}
                Fun: ${game?.tierReview?.scoreFun ?? 'N/A'}
                Potential: ${game?.tierReview?.scorePotential ?? 'N/A'}
                Graphic: ${game?.tierReview?.scoreGraphic ?? 'N/A'}
                UI: ${game?.tierReview?.scoreUi ?? 'N/A'}
                Audio: ${game?.tierReview?.scoreAudio ?? 'N/A'}
                Control: ${game?.tierReview?.scoreControl ?? 'N/A'}
                Stability: ${game?.tierReview?.scoreStability ?? 'N/A'}
                Buy: ${game?.tierReview?.scoreBuy ?? 'N/A'}`;

export const calculateFinalScore = (
  scores: Partial<
    Omit<
      Selectable<KyselyDatabase['tierReview']>,
      'gameSteamId' | 'publishDate' | 'reviewContent' | 'reviewTitle' | 'createdAt' | 'updatedAt'
    >
  >,
) => {
  let sumScore = 0;
  let sumWeight = 0;
  Object.values(SCORE_DEFINITIONS).forEach(scoreDef => {
    if (typeof scores[scoreDef.key] === 'number') {
      sumScore += scores[scoreDef.key]! * scoreDef.weight;
      sumWeight += scoreDef.weight;
    }
  });
  if (sumWeight === 0) return 0;
  const finalScore = Math.round(sumScore / sumWeight);
  return finalScore;
};

// Get dynamic color based on score (same logic as GameScoreSliders.vue)
export const getDynamicColor = (baseColor: string, score: number) => {
  // Convert score to saturation (0-100 -> 30-100)
  const saturation = 30 + (score / 100) * 70;
  // Convert hex to HSL and adjust saturation
  let hex = baseColor.replace('#', '');
  // Handle 3 chars hex code
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const add = max + min;
  const l = add * 0.5;

  let h = 0;
  if (diff !== 0) {
    switch (max) {
      case r:
        h = (g - b) / diff + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }

  return `hsl(${Math.round(h * 360)}, ${saturation.toFixed(2)}%, ${Math.round(l * 100)}%)`;
};

export const getDynamicColorWithAlpha = (baseColor: string, score: number, alpha: number) => {
  // Convert score to saturation (0-100 -> 30-100)
  const saturation = 30 + (score / 100) * 70;
  // Convert hex to HSL and adjust saturation
  let hex = baseColor.replace('#', '');
  // Handle 3 chars hex code
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const add = max + min;
  const l = add * 0.5;

  let h = 0;
  if (diff !== 0) {
    switch (max) {
      case r:
        h = (g - b) / diff + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }
    h /= 6;
  }

  return `hsla(${Math.round(h * 360)}, ${saturation.toFixed(2)}%, ${Math.round(l * 100)}%, ${alpha})`;
};

/**
 * Convert a score (0-100) to a color from red to green
 * @param score - Score value between 0 and 100
 * @returns HSL color string
 */
export function getScoreColor(score: number): string {
  // Clamp score between 0 and 100
  const clampedScore = Math.max(0, Math.min(100, score));

  // Convert score to hue: 0 (red) to 120 (green)
  const hue = (clampedScore / 100) * 120;

  // Use high saturation and medium lightness for vibrant colors
  return `hsl(${hue}, 70%, 30%)`;
}

/**
 * Get the scoreboard data for a game as a formatted object
 * @param game - BlogGameResult object
 * @returns Object with individual scores and final score
 */
export function getGameScoreboard(game: ComputedPropertiesOfGameSteam) {
  const tierReview = game.tierReview;
  if (!tierReview) return null;

  return {
    finalScore: tierReview.finalScore,
    scores: {
      bias: tierReview.scoreBias,
      creative: tierReview.scoreCreative,
      fun: tierReview.scoreFun,
      potential: tierReview.scorePotential,
      graphic: tierReview.scoreGraphic,
      ui: tierReview.scoreUi,
      audio: tierReview.scoreAudio,
      control: tierReview.scoreControl,
      stability: tierReview.scoreStability,
      buy: tierReview.scoreBuy,
    },
  };
}
