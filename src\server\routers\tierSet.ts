import { format } from 'date-fns';
import { jsonArrayFrom, jsonObjectFrom } from 'kysely/helpers/postgres';
import * as _ from 'lodash-es';
import { z } from 'zod';

import { TRPCError } from '@trpc/server';

import { type KyselyInstance, kysely } from '@/database/kysely';
import { applyComputedTierSet } from '@/database/tierSet';
import { slugifyWithSettings } from '@/helpers/utils';

import { createRouter, publicProcedure } from '../trpc';
import { filterSchema as tierItemFilterSchema } from './tierItem';

const filterSchema = z.object({
  slugs: z.array(z.string()).default([]),
  spaces: z.array(z.number()).default([]),
  tierLanes: z
    .object({
      includeCount: z.boolean().default(true),
    })
    .default({
      includeCount: true,
    }),
  tierItems: tierItemFilterSchema
    .pick({
      search: true,
      hasDemo: true,
      hasReview: true,
      tags: true,
      take: true,
      skip: true,
    })
    .extend({
      includeCount: z.boolean().default(true),
    })
    .default({ skip: 0, take: 100, includeCount: true }),
});

const getTierSetFullQuery = (
  { slugs = [], spaces = [] }: Partial<z.infer<typeof filterSchema>>,
  tx: KyselyInstance,
  userId?: string,
) => {
  let query = tx
    .selectFrom('tierSet as ts')
    .select(['label', 'slug', 'permissionType', 'spaceId'])
    .select(eb => [
      eb
        .exists(
          eb
            .selectFrom('playersOnSpaces as pos')
            .whereRef('pos.spaceId', '=', 'ts.spaceId')
            .innerJoin('player as p', 'p.id', 'pos.playerId')
            .where('p.userId', '=', userId!),
        )
        .as('canEdit'),
    ])
    .select(eb => [
      jsonArrayFrom(
        eb
          .selectFrom('tierLane as tl')
          .whereRef('tl.tierSetSlug', '=', 'ts.slug')
          .selectAll()
          .select(ebtl => [
            jsonArrayFrom(
              ebtl
                .selectFrom('tierItem as ti')
                .whereRef('ti.tierLaneSlug', '=', 'ts.slug')
                .select(['id', 'tierLaneSlug', 'reviewTitle', 'lexorank', 'gameSteamId']),
            ).as('tierItems'),
          ])
          .orderBy('score', 'desc'),
      ).as('tierLanes'),
    ]);

  if (slugs.length) {
    query = query.where('ts.slug', 'in', slugs);
  }

  if (spaces.length) {
    query = query.where('ts.spaceId', 'in', spaces);
  }

  return query;
};

export const tierSetRouter = createRouter({
  // ---------------------------
  retrieve: publicProcedure
    .input(
      z.object({
        slug: z.string(),
      }),
    )
    .query(async ({ input: { slug }, ctx: { user } }) => {
      const fullQuery = getTierSetFullQuery({ slugs: [slug] }, kysely, user?.id);
      const result = await fullQuery.executeTakeFirst();
      const tierItem = result ? applyComputedTierSet(result) : null;
      return tierItem;
    }),
  // ---------------------------
  retrieveMany: publicProcedure.input(filterSchema).query(async ({ input, ctx: { user } }) => {
    const fullQuery = getTierSetFullQuery(input, kysely, user?.id);
    const result = await fullQuery.execute();
    const tierSets = result.map(applyComputedTierSet);
    return tierSets;
  }),
  // ---------------------------
  create: publicProcedure
    .input(
      z.object({
        spaceId: z.number().int().positive(),
        displayName: z.string().optional(),
        label: z.string().optional(),
        slug: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const dateStr = format(new Date(), 'yyyy-MM-dd hh:mm:ss');
      const label = input.label ?? input.displayName + ' ' + dateStr;
      const slug = input.slug ?? slugifyWithSettings(input.displayName + ' ' + dateStr);

      const query = kysely
        .insertInto('tierSet')
        .values({
          slug,
          label,
          spaceId: input.spaceId,
        })
        .returningAll()
        .returning(eb => [eb.selectFrom('space').whereRef('space.id', '=', 'spaceId').as('space')]);

      const result = await query.executeTakeFirst();

      const tierSet = result ? applyComputedTierSet(result) : null;

      return tierSet;
    }),
  // ---------------------------
  update: publicProcedure
    .input(
      z.object({
        spaceId: z.number().int().positive().optional(),
        tierSetSlug: z.string(),
        label: z.string().optional(),
        slug: z.string().optional(),
        description: z.string().optional(),
        lexorank: z.string().optional(),
        mainColor: z.string().optional(),
        textColor: z.string().optional(),
        icon: z.string().optional(),
        score: z.number().optional(),
        permissionType: z.enum(['PUBLIC', 'PRIVATE']).optional(),
        tierLanes: z
          .array(
            z.object({
              tierLaneSlug: z.string(),
              label: z.string().optional(),
              slug: z.string(),
              lexorank: z.string().nullable().optional(),
              mainColor: z.string().optional(),
              textColor: z.string().optional(),
              icon: z.string().optional(),
              score: z.number().optional(),
            }),
          )
          .optional(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const { tierSetSlug: oldSlug, tierLanes, spaceId, slug, ...data } = input;
      const newTierSetSlug = slug || oldSlug;

      await kysely.transaction().execute(async trx => {
        const updateQuery = trx
          .updateTable('tierSet as ts')
          .where('ts.slug', '=', oldSlug)
          .set(data);

        const updateResult = await updateQuery.executeTakeFirst();
        console.log('updateResult', updateResult);
        if ((updateResult.numChangedRows ?? 0) == 0 && updateResult.numUpdatedRows == 0n) {
          const insertQuery = trx.insertInto('tierSet').values({
            ...data,
            spaceId,
            slug: newTierSetSlug,
          });
          const insertResult = await insertQuery.executeTakeFirst();
          console.log('insertResult', insertResult);
        }

        if (tierLanes && tierLanes.length > 0) {
          const oldLanes = await trx
            .selectFrom('tierLane as tl')
            .where('tl.tierSetSlug', '=', newTierSetSlug)
            .selectAll()
            .execute();

          const toAdd = tierLanes.filter(tl => !oldLanes.find(ol => ol.slug === tl.tierLaneSlug));
          const toRemove = oldLanes.filter(
            ol => !tierLanes.find(tl => tl.tierLaneSlug === ol.slug),
          );
          const toUpdate = tierLanes.filter(
            tl =>
              !_.isEqual(
                tl,
                oldLanes.find(ol => ol.slug === tl.tierLaneSlug),
              ),
          );

          // Add
          const addPromises = toAdd.map(({ tierLaneSlug, ...tl }) => {
            console.log('Adding lane', tierLaneSlug);
            return trx
              .insertInto('tierLane')
              .values({
                ...tl,
                tierSetSlug: newTierSetSlug,
              })
              .execute();
          });
          // Remove
          const removePromises = toRemove.map(({ slug }) => {
            console.log('Removing lane', slug);
            return trx
              .deleteFrom('tierLane as tl')
              .where('tl.slug', '=', slug)
              .where('tl.tierSetSlug', '=', newTierSetSlug)
              .execute();
          });
          // Update
          const updatePromises = toUpdate.map(({ tierLaneSlug, ...tl }) => {
            console.log('Updating lane', tierLaneSlug);
            return trx
              .updateTable('tierLane as tl')
              .set(tl)
              .where('tl.slug', '=', tierLaneSlug)
              .where('tl.tierSetSlug', '=', newTierSetSlug)
              .execute();
          });

          await Promise.all([...addPromises, ...removePromises, ...updatePromises]);
        }
      });

      const fullQuery = getTierSetFullQuery({ slugs: [newTierSetSlug] }, kysely, user?.id);
      const result = await fullQuery.executeTakeFirst();

      const tierSet = result ? applyComputedTierSet(result) : null;

      return tierSet;
    }),
  // ---------------------------
  delete: publicProcedure
    .input(
      z.object({
        tierSetSlug: z.string(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const { tierSetSlug } = input;

      const query = kysely
        .deleteFrom('tierSet as ts')
        .where('ts.slug', '=', tierSetSlug)
        .returningAll();

      const result = await query.executeTakeFirst();

      const tierSet = result ? applyComputedTierSet(result) : null;

      return tierSet;
    }),
});

export type TierSetRouter = typeof tierSetRouter;
