<script setup lang="ts">
import PrimeButton from 'primevue/button';
import PrimeInputText from 'primevue/inputtext';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import type { SortableEvent, SortableOptions } from 'sortablejs';
import Draggable from 'vuedraggable';

import { Icon } from '@iconify/vue';
import { useVModels } from '@vueuse/core';

import { cn } from '@/helpers/cn';
import { trpc } from '@/services/trpc';
import { pinia, useUserStore } from '@/stores/user';

const props = defineProps({
  tierSet: {
    type: Object as () => Parameters<typeof trpc.tierSet.update.mutate>[0] & {
      editing: boolean;
      loading: boolean;
    },
    required: true,
  },
  oldSlug: {
    type: String,
    required: true,
  },
});

const emit = defineEmits<{
  (e: 'update:tierSet', tierSet: Parameters<typeof trpc.tierSet.update.mutate>[0]): void;
  (e: 'finished', tierSet: Awaited<ReturnType<typeof trpc.tierSet.update.mutate>>): void;
  (e: 'deleted'): void;
}>();

const { tierSet } = useVModels(props, emit);

const toast = useToast();
const confirm = useConfirm();

const userStore = useUserStore(pinia);
const { space } = storeToRefs(userStore);

const options = computed<SortableOptions>(() => {
  return {
    draggable: '.draggable',
    filter: '.ignore-drag',
    handle: '.drag-handle',
    preventOnFilter: false,
    delay: 150,
    delayOnTouchOnly: true,
    animation: 150,
    easing: 'cubic-bezier(1, 0, 0, 1)',
    forceFallback: true,
    scroll: true,
    bubbleScroll: true,
    sort: true,
  };
});

function onEnd(e: SortableEvent) {
  if (e.oldIndex === undefined || e.newIndex === undefined) return;
  const item = tierSet.value.tierLanes!.splice(e.oldIndex, 1)[0];
  tierSet.value.tierLanes!.splice(e.newIndex, 0, item);
}

function addTierLane() {
  if (!tierSet.value.tierLanes) {
    tierSet.value.tierLanes = [];
  }
  const newSlug = `new-${tierSet.value.tierLanes.length + 1}`;
  tierSet.value.tierLanes.push({
    label: newSlug,
    slug: newSlug,
    tierLaneSlug: newSlug,
    mainColor: '#000000',
    textColor: '#ffffff',
    lexorank: `${tierSet.value.tierLanes.length + 1}`,
  });
}

async function saveEdit() {
  try {
    tierSet.value.loading = true;
    const newTierSet = await trpc.tierSet.update.mutate({
      tierSetSlug: tierSet.value.tierSetSlug,
      label: tierSet.value.label,
      slug: tierSet.value.slug,
      spaceId: space.value?.id!,
      tierLanes: tierSet.value.tierLanes?.map((tl, tli) => ({
        ...tl,
        lexorank: `${tli}`.padStart(3, '0'),
      })),
    });
    toast.add({
      severity: 'success',
      summary: `Saved TierSet ${tierSet.value.label}`,
      life: 10000,
    });
    emit('finished', newTierSet);
  } catch (e: any) {
    console.error('tierSet saveEdit:', e);
    toast.add({
      severity: 'error',
      summary: "Can't save TierSet",
      detail: e.message,
      life: 20000,
    });
  }
  tierSet.value.loading = false;
}

function cancelEdit() {
  tierSet.value.editing = false;
}

function deleteTierSet() {
  confirm.require({
    message: `Are you sure you want to delete "${tierSet.value.label}" ?`,
    header: 'Hold !!!',
    accept: async () => {
      await trpc.tierSet.delete.mutate({
        tierSetSlug: tierSet.value.tierSetSlug,
      });
      toast.add({
        severity: 'info',
        summary: `Deleted TierSet ${tierSet.value.label}`,
        life: 10000,
      });
      emit('deleted');
    },
    reject: () => {
      //
    },
  });
}
</script>

<template>
  <div class="flex flex-col gap-1">
    <div class="flex gap-1">
      <div class="relative flex flex-col">
        <Icon icon="lucide:text" class="absolute my-2 mr-2 ml-2 text-xs" />
        <PrimeInputText
          v-model="tierSet.label"
          inputId="set-label"
          :placeholder="tierSet.label ?? ''"
          :disabled="tierSet.loading"
          :class="cn('py-1! pl-6! text-xs!', 'placeholder:text-(--main)! placeholder:opacity-30')"
        />
      </div>
      <div class="relative flex flex-col">
        <Icon icon="lucide:text" class="absolute my-2 mr-2 ml-2 text-xs" />
        <PrimeInputText
          v-model="tierSet.slug"
          inputId="set-slug"
          :placeholder="oldSlug ?? ''"
          :disabled="tierSet.loading"
          :class="cn('py-1! pl-6! text-xs!', 'placeholder:text-(--main)! placeholder:opacity-30')"
        />
      </div>

      <PrimeButton
        :class="cn('rounded-md bg-blue-900! text-base', 'px-1')"
        :loading="tierSet.loading"
        @click="saveEdit"
      >
        <Icon icon="lucide:save" class="text-xs" />
      </PrimeButton>
      <PrimeButton
        :class="cn('rounded-md bg-gray-600! text-base', 'px-1')"
        :loading="tierSet.loading"
        @click="cancelEdit"
      >
        <Icon icon="lucide:x" class="text-xs" />
      </PrimeButton>
      <PrimeButton
        :class="cn('rounded-md bg-transparent! text-base text-red-400!', 'px-1')"
        :loading="tierSet.loading"
        @click="deleteTierSet"
      >
        <Icon icon="lucide:trash-2" class="text-xs" />
      </PrimeButton>
    </div>

    <Draggable
      v-model="tierSet.tierLanes"
      v-bind="options"
      item-key="slug"
      tag="div"
      class="flex flex-col gap-0.5 rounded-md"
      @end="onEnd"
    >
      <template
        #item="{
          element: lane,
          index: i,
        }: {
          element: NonNullable<typeof tierSet.value.tierLanes>[0];
          index: number;
        }"
      >
        <div
          :key="lane.slug"
          :style="{
            '--main': lane.mainColor || 'var(--color-primary)',
            '--text': lane.textColor,
          }"
          class="draggable flex h-6 gap-0.5 rounded-md ring-1 ring-gray-600"
        >
          <PrimeCustomColorPicker
            v-model="lane.mainColor"
            :disabled="tierSet.loading"
            :pt="{ preview: { class: 'h-6 w-4' } }"
            class="drag-handle"
          />
          <PrimeCustomColorPicker
            v-model="lane.textColor"
            class="drag-handle absolute"
            :disabled="tierSet.loading"
            :pt="{ preview: { class: 'absolute left-0 h-4 w-2 rounded-xs' } }"
          />
          <PrimeInputText
            v-model="lane.icon"
            inputId="lane-icon"
            :placeholder="lane.icon ?? ''"
            :disabled="tierSet.loading"
            :class="
              cn(
                'w-5! bg-(--main)! py-0! pr-0! pl-0! text-xs! text-(--text)!',
                'placeholder:text-(--main)! placeholder:opacity-30',
              )
            "
          />
          <PrimeInputText
            v-model="lane.label"
            inputId="lane-label"
            :placeholder="lane.label ?? ''"
            :disabled="tierSet.loading"
            :class="
              cn(
                'bg-(--main)! py-0! pl-0! text-xs! text-(--text)!',
                'placeholder:text-(--main)! placeholder:opacity-30',
              )
            "
          />
          <PrimeInputText
            v-model="lane.slug"
            inputId="lane-slug"
            :placeholder="lane.tierLaneSlug ?? ''"
            :disabled="tierSet.loading"
            :class="
              cn(
                'bg-(--main)! py-0! pl-0! text-xs! text-(--text)!',
                'placeholder:text-(--main)! placeholder:opacity-30',
              )
            "
          />
          <PrimeButton
            :class="cn('rounded-md bg-transparent! text-base text-red-400!', 'w-12 px-1')"
            :loading="tierSet.loading"
            @click="tierSet.tierLanes?.splice(i, 1)"
          >
            <Icon icon="lucide:trash-2" class="text-xs" />
          </PrimeButton>
        </div>
      </template>
    </Draggable>
    <PrimeButton
      :class="cn('rounded-md bg-green-800! text-base', 'px-1')"
      :loading="tierSet.loading"
      @click="addTierLane"
    >
      <Icon icon="lucide:list-plus" class="text-xs" />
    </PrimeButton>
  </div>
</template>
