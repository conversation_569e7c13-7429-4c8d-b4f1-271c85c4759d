<script setup lang="ts">
import { Icon } from '@iconify/vue';

import LogoFlat from '@/assets/logo-flat.svg';
import { cn } from '@/helpers/cn';
import { CONST } from '@/helpers/constants';

import TheAuthButton from './TheAuthButton.vue';
import TheOfficeButton from './TheOfficeButton.vue';

defineProps({
  showAuth: {
    type: Boolean,
    default: false,
  },
});

const isCollapsed = ref(false);

// Methods
function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value;
}
</script>

<template>
  <header id="main-header" :class="cn('group/header', 'relative mx-auto max-w-3xl')">
    <button
      :class="
        cn(
          'absolute -top-8 left-1/2',
          'transition-transform',
          '-translate-x-1/2 translate-y-full',
          isCollapsed ? 'rotate-180' : '',
        )
      "
      aria-label="Toggle header"
      @click="toggleCollapse"
    >
      <Icon
        icon="lucide:chevron-up"
        :class="
          cn('text-gray-500 hover:text-gray-700', 'dark:text-gray-400 dark:hover:text-gray-200')
        "
      />
    </button>
    <div
      :class="cn('relative flex items-center gap-3', isCollapsed ? 'h-0 overflow-hidden' : 'mb-8')"
    >
      <div :class="cn('flex sm:flex-col')">
        <a href="/" :class="cn('flex grow items-center grayscale hover:filter-none')">
          <img :class="cn('me-3 h-10 sm:h-16')" :src="LogoFlat.src" alt="logo" />
          <span :class="cn('text-xl font-bold sm:text-2xl')">
            {{ CONST.title }}
          </span>
          <span :class="cn('hidden text-xl font-bold sm:text-2xl md:inline')">
            - {{ CONST.titleExt }}
          </span>
        </a>
      </div>
      <TheAuthButton v-if="showAuth" />
      <TheOfficeButton v-else />
    </div>
  </header>
</template>
