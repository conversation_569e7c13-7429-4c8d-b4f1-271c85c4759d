<script setup lang="ts">
import * as _ from 'lodash-es';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import type { SortableEvent, SortableOptions } from 'sortablejs';
import type { AutoScrollOptions } from 'sortablejs/plugins';
import type { MaybeRef } from 'vue';
import Draggable from 'vuedraggable';

import { Icon } from '@iconify/vue';
import { vIntersectionObserver } from '@vueuse/components';
import { watchDebounced } from '@vueuse/core';

import type { TierItemForMainPage } from '@/database/tierItem';
import type { TierLaneForMainPage, TierSetForMainPage } from '@/database/tierSet';
import { getLexorankBetween } from '@/helpers/utils';
import { trpc } from '@/services/trpc';
import { pinia, useItemStore } from '@/stores/item';

import TierItemCard from './TierItemCard.vue';
import TheAddItem from './add-item/TheAddItem.vue';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
  canAdd: {
    type: Boolean,
    default: false,
  },
  canEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:lane', 'delete:lane']);

const itemStore = useItemStore(pinia);
const {
  itemInfoMap,
  clonedIdList,
  gameSteamOnTierSetMap,
  fShowingItemTypes,
  fSearchText,
  hasDemo,
  hasReview,
} = storeToRefs(itemStore);
const { assignItemInfoByList, assignItemInfoByObject } = itemStore;

const toast = useToast();

const tierSet = inject<Ref<TierSetForMainPage>>('tierSet', ref() as Ref<TierSetForMainPage>);
const parent = inject<Ref<TierSetForMainPage['tierLanes'] | TierLaneForMainPage['tierItems']>>(
  'parent',
  ref() as Ref<TierSetForMainPage['tierLanes']>,
);
const tierLane = computed({
  get() {
    const child = parent.value[props.index];
    return ('fromLane' in child ? child.fromLane : child) as TierLaneForMainPage;
  },
  set(val) {
    const child = parent.value[props.index];
    if ('fromLane' in child) {
      child.fromLane = val as any;
    } else {
      parent.value[props.index] = val as any;
    }
  },
});
provide('tierLane', tierLane);

const parentForChildren = computed({
  get() {
    return tierLane.value.tierItems;
  },
  set(val) {
    tierLane.value.tierItems = val;
  },
});
provide('parent', parentForChildren);

const laneTierItems = computed({
  get() {
    return (tierLane.value.tierItems as TierItemForMainPage[]) ?? [];
  },
  set(val) {
    assignItemInfoByList(val);
    tierLane.value = {
      ...tierLane.value,
      tierItems: val,
    };
  },
});

const displayingItems = computed(() => {
  return laneTierItems.value
    .filter(item => !!item)
    .map(item => {
      const isDisplaying = displayingIds.value.includes(item.id);
      const isNotCloned = !clonedIdList.value.includes(item.id);
      // const isPassShowingFilter = fShowingItemTypes.value.includes(item.type);
      // const isPassHasDemoFilter = (() => {
      //   switch (hasDemo.value) {
      //     case true:
      //       return item.gameSteam?.hasDemo;
      //     case false:
      //       return !item.gameSteam?.hasDemo;
      //     default:
      //       return true;
      //   }
      // })();

      return {
        ...item,
        show: isDisplaying && isNotCloned,
        // isPassShowingFilter &&
        // isPassHasDemoFilter &&
      };
    });
});

const tierItemCardsContainer = ref<HTMLElement>();
const draggingIndexes = ref<number[]>([]);
const isAddItemModalActive = ref(false);
const isEditingLane = ref(false);
const isLoadingUpdate = ref(false);

const connectedToDb = inject<MaybeRef<boolean>>('connectedToDb', false);
const laneDataToUpdate = ref({ ...tierLane.value });

const isLoadingMoreGames = ref(false);
const totalGameCount = ref(tierLane.value._count?.tierItems ?? Number.POSITIVE_INFINITY);
const currentGameCount = computed(() => laneTierItems.value.length);
const isLoadedAllGames = computed(
  () => currentGameCount.value >= totalGameCount.value || !unref(connectedToDb),
);

function onLastTierItemIntersectionObserver([{ isIntersecting }]: IntersectionObserverEntry[]) {
  if (isIntersecting && !isLoadingMoreGames.value) {
    loadMoreGames();
  }
}

const loadMoreGames = _.debounce(async () => {
  if (isLoadedAllGames.value) {
    return;
  }
  isLoadingMoreGames.value = true;

  const { data, total } = await trpc.tierItem.list.query({
    sets: [tierLane.value.tierSetSlug],
    lanes: [tierLane.value.slug],
    search: fSearchText.value,
    hasDemo: hasDemo.value,
    hasReview: hasReview.value,
    skip: currentGameCount.value,
    take: 50,
  });

  const tierItems = data;
  totalGameCount.value = total;

  laneTierItems.value = [...laneTierItems.value, ...tierItems];
  displayingIds.value = laneTierItems.value.map(r => r.id);

  isLoadingMoreGames.value = false;
}, 1000);

async function applyUpdate() {
  isLoadingUpdate.value = true;
  if (unref(connectedToDb)) {
    try {
      const { tierItems, ...updated } = await trpc.tierLane.update.mutate({
        tierSetSlug: tierLane.value.tierSetSlug,
        tierLaneSlug: tierLane.value.slug,
        ...(laneDataToUpdate.value as any),
      });
      tierLane.value = { ...tierLane.value, ...updated };
    } catch (err) {
      console.error(err);
      toast.add({
        severity: 'error',
        summary: "Can't update TierLane",
        life: 20000,
      });
    }
  } else {
    tierLane.value = { ...tierLane.value, ...laneDataToUpdate.value };
  }
  isEditingLane.value = false;
  isLoadingUpdate.value = false;
}

function cancelUpdate() {
  laneDataToUpdate.value = { ...tierLane.value };
  isEditingLane.value = false;
}

async function createTierItems<T extends Pick<TierItemForMainPage, 'id' | 'tierSetSlug' | 'type'>>(
  tierItems: T[],
) {
  if (unref(connectedToDb)) {
    const createdItems = await trpc.tierItem.createMany.mutate({
      items: tierItems,
    });
    return createdItems;
  }
  return tierItems.map(item => ({
    ...item,
    id: new Date().valueOf(),
    clonedFromTierItemId: item.id,
  }));
}

async function updateTierItem(tierItem: Partial<TierItemForMainPage>) {
  if (unref(connectedToDb)) {
    const updated = await trpc.tierItem.update.mutate({
      tierSetSlug: tierLane.value.tierSetSlug,
      id: tierItem.id!,
      ...(tierItem as any),
    });
    return updated;
  }
  return tierItem;
}

function pullLogic(from: { el: HTMLElement }, to: { el: HTMLElement }) {
  const fromTierSlug = from.el.dataset['tierSlug']!;
  const toTierSlug = to.el.dataset['tierSlug']!;
  // if (fromTierSlug !== toTierSlug) {
  //   return 'clone';
  // }
  return true;
}

function parseEventData(e: SortableEvent) {
  const fromTierSlug = e.from.dataset['tierSlug']!;
  const toTierSlug = e.to.dataset['tierSlug']!;
  const fromLaneSlug = e.from.dataset['laneSlug']!;
  const toLaneSlug = e.to.dataset['laneSlug']!;
  const oldIndex = +e.item.dataset['index']!;
  const fromId = +e.item.dataset['itemId']!;
  const prevLexorank =
    (e.item.previousElementSibling as HTMLElement)?.dataset?.['lexorank'] ?? null;
  const nextLexorank = (e.item.nextElementSibling as HTMLElement)?.dataset?.['lexorank'] ?? null;

  return {
    fromTierSlug,
    toTierSlug,
    fromLaneSlug,
    toLaneSlug,
    oldIndex,
    fromId,
    prevLexorank,
    nextLexorank,
  };
}

function getLogColor() {
  return `background:${tierLane.value.mainColor}; color:${tierLane.value.textColor}; border: 1px solid ${tierLane.value.textColor};`;
}

// const logEvent = (e1: Event, e2?: Event) => {
//   if (e2) {
//     console.log(e1, e2);
//   } else {
//     console.log(e1);
//   }
// };

function onStart(e: SortableEvent) {
  if (e.oldIndex === undefined) return;
  const { oldIndex } = parseEventData(e);
  draggingIndexes.value.push(oldIndex);
}

function onEnd(e: SortableEvent) {
  if (e.oldIndex === undefined) return;
  const { oldIndex } = parseEventData(e);
  const index = draggingIndexes.value.findIndex(i => i === oldIndex);
  draggingIndexes.value.splice(index, 1);
}

function onRemove(e: SortableEvent) {
  if (e.oldIndex === undefined) return;
  const eventData = parseEventData(e);
  const { fromTierSlug, toTierSlug, oldIndex } = eventData;

  console.log('%c onRemove:', getLogColor(), { caller: tierLane.value.slug, e, eventData });

  if (fromTierSlug === toTierSlug) {
    laneTierItems.value = [
      ...laneTierItems.value.slice(0, oldIndex),
      ...laneTierItems.value.slice(oldIndex + 1),
    ];
    // Remove old element, new one is already cloned
    e.item.remove();
  } else {
    e.from.appendChild(e.item);
  }

  totalGameCount.value -= 1;

  console.log('%c Removed:', getLogColor(), { caller: tierLane.value.slug, e, eventData });
}

async function onAdd(e: SortableEvent) {
  if (e.oldIndex === undefined) return;
  if (e.newIndex === undefined) return;

  const eventData = parseEventData(e);
  const { fromTierSlug, toTierSlug, toLaneSlug, fromId, prevLexorank, nextLexorank } = eventData;

  console.log('%c onAdd:', getLogColor(), { caller: tierLane.value.slug, e, eventData });

  let item = itemInfoMap.value[fromId];

  item.lexorank = getLexorankBetween(prevLexorank, nextLexorank);
  item.tierLaneSlug = toLaneSlug;
  item.tierSetSlug = toTierSlug;

  totalGameCount.value += 1;

  await nextTick();

  if (fromTierSlug === toTierSlug) {
    updateTierItem(item);
  } else {
    item = (await createTierItems([item]))[0];
  }

  if (item) {
    laneTierItems.value = [...laneTierItems.value, item].toSorted((a, b) =>
      (a.lexorank ?? '')?.localeCompare(b.lexorank ?? ''),
    );
    displayingIds.value.push(item.id);
    // Hide cloned element since vue sortable is so bugged
    // if (clonedIdList.value.includes(fromId)) {
    //   console.log('Hiding source of cloned item from:', fromLaneSlug);
    //   const selector = `[data-lane-slug="${fromLaneSlug}"] > [data-item-id="${fromId}"]`;
    //   const itemEl: HTMLElement = document.querySelector(selector)!;
    //   if (itemEl) {
    //     console.log('Hid item:', selector, itemEl);
    //     itemEl.style.display = 'none';
    //     console.log(tierLane.value.label, itemEl);
    //   }
    // }
    console.log('%c Added:', getLogColor(), { caller: tierLane.value.slug, e, eventData, item });
  }
}

async function onUpdate(e: SortableEvent) {
  if (e.oldIndex === undefined || e.newIndex === undefined) return;

  const eventData = parseEventData(e);
  const { toTierSlug, toLaneSlug, oldIndex, prevLexorank, nextLexorank } = eventData;

  console.log('%c onUpdate:', getLogColor(), { caller: tierLane.value.slug, e, eventData });

  const item = laneTierItems.value[oldIndex];
  item.lexorank = getLexorankBetween(prevLexorank, nextLexorank);
  item.tierLaneSlug = toLaneSlug;
  item.tierSetSlug = toTierSlug;

  await nextTick();
  laneTierItems.value = [...laneTierItems.value].toSorted((a, b) =>
    (a.lexorank ?? '')?.localeCompare(b.lexorank ?? ''),
  );

  updateTierItem(item);

  console.log('%c Updated:', getLogColor(), {
    caller: tierLane.value.slug,
    e,
    eventData,
    item,
  });
}

const options = computed<SortableOptions | AutoScrollOptions>(() => {
  return {
    group: { name: 'games', put: props.canEdit, pull: pullLogic, revertClone: true },
    draggable: '.draggable',
    filter: '.ignore-drag',
    preventOnFilter: false,
    delay: 150,
    delayOnTouchOnly: true,
    animation: 150,
    easing: 'cubic-bezier(1, 0, 0, 1)',
    forceFallback: true,
    scroll: true,
    bubbleScroll: true,
    sort: props.canEdit,
  };
});

const displayingIds = ref<number[]>([]);

const confirm = useConfirm();
function deleteLane() {
  confirm.require({
    message: `Are you sure you want to delete "${tierLane.value.label}" ?`,
    header: 'Hold !!!',
    accept: () => {
      emit('delete:lane');
    },
    reject: () => {
      //
    },
  });
}

function addItems(tierItems: TierItemForMainPage[]) {
  laneTierItems.value = [...laneTierItems.value, ...tierItems];
}

function deleteItem(index: number) {
  const item = laneTierItems.value[index];
  confirm.require({
    message: `Are you sure you want to delete "${item.fullTitle}" ?`,
    header: 'Hold !!!',
    accept: () => {
      execute();
    },
    reject: () => {
      //
    },
  });

  async function execute() {
    isLoadingUpdate.value = true;
    if (unref(connectedToDb)) {
      await trpc.tierItem.delete.mutate({
        tierSetSlug: tierLane.value.tierSetSlug,
        id: item.id,
      });
    }

    laneTierItems.value = [
      ...laneTierItems.value.slice(0, index),
      ...laneTierItems.value.slice(index + 1),
    ];
    assignItemInfoByObject({ [item.id]: null });
    isLoadingUpdate.value = false;
  }
}

watchDebounced(
  [fSearchText, hasDemo, hasReview],
  () => {
    totalGameCount.value = 1;
  },
  { debounce: 1_000 },
);

onMounted(() => {
  assignItemInfoByList(laneTierItems.value);
  displayingIds.value = laneTierItems.value.map(r => r.id);
  if (
    laneTierItems.value?.length === 0 &&
    tierLane.value._count?.tierItems > 0 &&
    !isLoadingMoreGames.value
  ) {
    loadMoreGames();
  }
});
</script>

<template>
  <div
    :class="[
      'group/lane relative flex w-full',
      'rounded-lg border-2 border-solid border-(--main)',
      isEditingLane ? 'flex-wrap md:flex-nowrap' : '',
    ]"
    :style="{
      '--main': laneDataToUpdate.mainColor ?? tierLane.mainColor!,
      '--text': laneDataToUpdate.textColor ?? tierLane.textColor!,
    }"
  >
    <div
      :class="[
        'absolute -inset-px -z-10 h-[101%] w-[101%] opacity-20',
        'transition-all duration-1000',
        'rounded-xl bg-linear-to-r from-(--main) via-[#000] to-(--main) blur-md',
        'group-hover/lane:-inset-1 group-hover/lane:opacity-50 group-hover/lane:blur-lg',
        'group-hover/lane:animate-pulse group-hover/lane:duration-200',
      ]"
    ></div>
    <div
      :class="[
        'relative flex flex-row',
        isEditingLane ? 'w-full md:w-[30%]' : 'w-[20%] md:w-[10%]',
        'items-center justify-center overflow-hidden bg-(--main) text-center',
        'font-black tracking-wide text-(--text)',
      ]"
    >
      <!-- Showing -->
      <div
        :class="[
          'handle flex items-center justify-center',
          isEditingLane ? 'w-1/2' : 'w-full',
          { 'cursor-move': canEdit },
        ]"
      >
        <span class="p-1">{{ tierLane.label }}</span>
        <div
          v-if="canEdit"
          :class="[
            'absolute top-1 right-1',
            'cursor-pointer text-sm text-(--text)',
            'opacity-0 transition-opacity group-hover/lane:opacity-70',
          ]"
          @click.stop="isEditingLane = true"
        >
          <Icon icon="lucide:settings" />
        </div>
      </div>
      <!-- Editing -->
      <div
        v-if="isEditingLane"
        class="flex w-full grow flex-col justify-start gap-1 p-1 text-xs md:w-1/4"
      >
        <div class="relative flex flex-col">
          <Icon icon="lucide:text" class="absolute my-2 mr-2 ml-2 text-xs text-(--main)" />
          <PrimeInputText
            v-model="laneDataToUpdate.label"
            inputId="lane-label"
            :placeholder="tierLane.label ?? ''"
            :class="[
              'bg-(--text)! py-1! pl-6! text-xs! text-(--main)!',
              'placeholder:text-(--main)! placeholder:opacity-30',
            ]"
          />
          <!-- <Icon icon="lucide:code" class="text-xs absolute ml-2 mr-2 my-2 text-(--main)" />
          <PrimeInputText
            v-model="laneDataToUpdate.slug"
            inputId="lane-slug"
            :placeholder="lane.slug ?? ''"
            :class="[
              'text-xs! pl-6! py-1! bg-(--text)! text-(--main)!',
              'placeholder:text-(--main)! placeholder:opacity-30',
            ]"
          /> -->
        </div>
        <div class="flex items-center justify-center gap-1">
          <Icon icon="lucide:paint-bucket" class="inline text-lg"></Icon>
          <PrimeCustomColorPicker v-model="laneDataToUpdate.mainColor" />
          <Icon icon="lucide:pencil-line" class="inline text-lg"></Icon>
          <PrimeCustomColorPicker v-model="laneDataToUpdate.textColor" />
        </div>

        <div class="my-1 flex justify-center gap-0.5">
          <PrimeButton
            class="text-theme-text! w-8/12! justify-center bg-green-600! text-xs!"
            @click="applyUpdate"
            :loading="isLoadingUpdate"
          >
            <Icon icon="lucide:save" />
          </PrimeButton>
          <PrimeButton
            class="w-4/12! justify-center bg-gray-600! text-xs! text-gray-200!"
            @click="cancelUpdate"
          >
            <Icon icon="mdi:cancel" />
          </PrimeButton>
          <PrimeButton
            :class="[
              'absolute top-0 left-0 m-0.5 rounded-md bg-transparent! p-1 text-red-600! opacity-70',
              'cursor-pointer hover:bg-red-800! hover:opacity-100 hover:ring-2 hover:ring-red-700',
              'hover:animate-wiggle-more hover:animate-duration-75 hover:animate-infinite',
            ]"
            @click="deleteLane"
            :loading="isLoadingUpdate"
          >
            <Icon icon="lucide:trash"></Icon>
          </PrimeButton>
          <PrimeConfirmDialog>
            <template #icon>
              <Icon icon="lucide:trash" class="mr-2 inline"></Icon>
            </template>
          </PrimeConfirmDialog>
        </div>
      </div>
    </div>

    <Draggable
      v-model="displayingItems"
      item-key="id"
      tag="div"
      ref="tierItemCardsContainer"
      v-bind="options"
      :class="['flex max-h-64 min-h-[80px] w-full flex-wrap overflow-auto', 'm-0.5 gap-0.5']"
      :data-tier-slug="tierLane.tierSetSlug"
      :data-lane-slug="tierLane.slug"
      :key="`${canAdd}` /* Force refresh because Sortable is too stupid */"
      @start="onStart"
      @end="onEnd"
      @add="onAdd"
      @update="onUpdate"
      @remove="onRemove"
    >
      <template
        #item="{
          element: tierItem,
          index: i,
        }: {
          element: (typeof displayingItems)['value'][number];
          index: number;
        }"
      >
        <Transition name="fade">
          <div
            :class="[
              'draggable relative rounded bg-gray-500/5',
              tierItem.show ? '' : 'hidden',
              tierItem.type === 'FROM_LANE' ? 'w-full' : 'w-20',
            ]"
            :data-item-id="tierItem.id"
            :data-steam-id="tierItem.gameSteam?.steamId"
            :data-lexorank="tierItem.lexorank"
            :data-index="i"
            :key="tierItem.id + '_' + tierItem.show"
          >
            <div v-if="tierItem.type === 'FROM_LANE' && tierItem.fromLane">
              <!-- prettier-ignore -->
              <TierLane :index="i" :can-edit="false" />
            </div>
            <TierItemCard
              v-else
              :index="i"
              :is-dragging="draggingIndexes.includes(i)"
              :is-compact="false /* i >= 48 */"
              :root-container="tierItemCardsContainer"
              :can-edit="canEdit"
              :key="tierLane.slug"
            />
            <div
              v-if="isEditingLane"
              :class="[
                'absolute top-0 right-0 m-0.5 rounded-md bg-red-500 p-1 opacity-70',
                'cursor-pointer hover:bg-red-600 hover:opacity-100 hover:ring-2 hover:ring-red-700',
                'hover:animate-pulse',
              ]"
            >
              <a class="" @click="() => deleteItem(i)">
                <Icon icon="lucide:x"></Icon>
              </a>
            </div>
          </div>
        </Transition>
      </template>
      <template #footer>
        <!-- Count -->
        <!-- Infinity Scroll trigger -->
        <div
          v-if="!isLoadedAllGames"
          :class="['m-2 flex w-20 items-center justify-center', 'text-center text-white/20']"
          v-intersection-observer="onLastTierItemIntersectionObserver"
        >
          {{ currentGameCount }}/{{ totalGameCount }}
        </div>
        <!-- Add tierItem button -->
        <div
          v-if="canAdd"
          :class="[
            'group/card',
            'm-2 flex max-h-[100px] w-20 cursor-pointer items-center justify-center',
            'rounded-lg outline-2 outline-offset-2 outline-gray-400/80 outline-dashed',
            'hover:my-3 hover:text-lg hover:font-black hover:outline-4 hover:outline-offset-4',
            'hover:animate-jump transition-all',
            'bg-gray-500/5 hover:bg-(--main) hover:text-(--text)',
          ]"
          @click.stop="isAddItemModalActive = true"
        >
          <Icon icon="lucide:plus" class="mr-1 group-hover/card:animate-spin" />
          <span>New</span>
        </div>
        <!-- Infinity Scroll Loading... -->
        <div
          v-if="isLoadingMoreGames"
          class="flex h-24 w-full items-center justify-center rounded-md bg-black/50"
        >
          <div class="animate-bounce">Loading more...</div>
        </div>
      </template>
    </Draggable>

    <TheAddItem
      v-if="canAdd"
      v-model:visible="isAddItemModalActive"
      :lane="tierLane"
      @added="addItems"
    >
    </TheAddItem>
  </div>
</template>

<style scss="scss" scoped>
@reference '@/styles/global.css';

.sortable-ghost {
  @apply animate-duration-500 animate-pulse;
}
.sortable-drag {
  @apply animate-fade animate-duration-300 animate-once rounded-full;
}
</style>
