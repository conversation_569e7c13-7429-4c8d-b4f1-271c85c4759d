import { describe, expect, it } from 'vitest';

import type { BlogGameResult } from '../src/server/routers/blog';

// Mock data for testing
const createMockGame = (name: string, score: number): BlogGameResult =>
  ({
    steamId: Math.floor(Math.random() * 1000000),
    gameName: name,
    slug: name.toLowerCase().replace(/\s+/g, '-'),
    gameUrl: `https://store.steampowered.com/app/${Math.floor(Math.random() * 1000000)}`,
    shortDescription: `Test description for ${name}`,
    releaseDate: '2024-01-01',
    developers: ['Test Developer'],
    publishers: ['Test Publisher'],
    hasImageHeader: true,
    hasImageHeroCapsule: true,
    hasImageLibraryHero: true,
    hasImageLibraryPoster: true,
    hasImagePageBgBlur: true,
    movies: [],
    screenshots: [
      {
        path_full: `https://placehold.co/400x300/222/FFF?text=${name.split(' ').join('+')}`,
      },
    ],
    tierReview: {
      gameSteamId: Math.floor(Math.random() * 1000000),
      finalScore: score,
      scoreBias: score,
      scoreCreative: score,
      scoreFun: score,
      scorePotential: score,
      scoreGraphic: score,
      scoreUi: score,
      scoreAudio: score,
      scoreControl: score,
      scoreStability: score,
      scoreBuy: score,
      reviewTitle: `Review for ${name}`,
      reviewContent: `Test review content for ${name}`,
      publishDate: '2024-01-01',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    tierItems: [],
    imageFinalWide: `https://placehold.co/400x300/222/FFF?text=${name.split(' ').join('+')}`,
    imageFinalTall: `https://placehold.co/400x300/222/FFF?text=${name.split(' ').join('+')}`,
    imagePageBgBlur: `https://placehold.co/400x300/222/FFF?text=${name.split(' ').join('+')}`,
    isReviewed: true,
    isPlayed: true,
    isSkipped: false,
  }) as unknown as BlogGameResult;

describe('RankingBox Component Logic', () => {
  describe('Game Sorting and Filtering', () => {
    it('should sort games by score in descending order', () => {
      const mockGames = [
        createMockGame('Game A', 75),
        createMockGame('Game B', 90),
        createMockGame('Game C', 85),
        createMockGame('Game D', 95),
        createMockGame('Game E', 80),
      ];

      // Sort games by final score (highest first)
      const sortedGames = mockGames
        .filter(
          game => game.tierReview?.finalScore !== null && game.tierReview?.finalScore !== undefined,
        )
        .sort((a, b) => (b.tierReview?.finalScore || 0) - (a.tierReview?.finalScore || 0));

      expect(sortedGames).toHaveLength(5);
      expect(sortedGames[0].gameName).toBe('Game D'); // 95
      expect(sortedGames[1].gameName).toBe('Game B'); // 90
      expect(sortedGames[2].gameName).toBe('Game C'); // 85
      expect(sortedGames[3].gameName).toBe('Game E'); // 80
      expect(sortedGames[4].gameName).toBe('Game A'); // 75
    });

    it('should filter out games without scores', () => {
      const mockGames = [
        createMockGame('Game A', 75),
        createMockGame('Game B', 90),
        { ...createMockGame('Game C', 0), tierReview: null }, // No review
        createMockGame('Game D', 85),
      ];

      const filteredGames = mockGames.filter(
        game => game.tierReview?.finalScore !== null && game.tierReview?.finalScore !== undefined,
      );

      expect(filteredGames).toHaveLength(3);
      expect(filteredGames.map(g => g.gameName)).toEqual(['Game A', 'Game B', 'Game D']);
    });

    it('should correctly split top 10 into podium and runner-ups', () => {
      const mockGames = Array.from({ length: 15 }, (_, i) =>
        createMockGame(`Game ${i + 1}`, 100 - i),
      );

      const sortedGames = mockGames
        .filter(
          game => game.tierReview?.finalScore !== null && game.tierReview?.finalScore !== undefined,
        )
        .sort((a, b) => (b.tierReview?.finalScore || 0) - (a.tierReview?.finalScore || 0));

      const topGames = sortedGames.slice(0, 10);
      const podiumGames = topGames.slice(0, 3);
      const runnerUps = topGames.slice(3, 10);

      expect(topGames).toHaveLength(10);
      expect(podiumGames).toHaveLength(3);
      expect(runnerUps).toHaveLength(7);

      // Check podium games are the top 3
      expect(podiumGames[0].tierReview?.finalScore).toBe(100); // 1st place
      expect(podiumGames[1].tierReview?.finalScore).toBe(99); // 2nd place
      expect(podiumGames[2].tierReview?.finalScore).toBe(98); // 3rd place

      // Check runner-ups are 4th-10th place
      expect(runnerUps[0].tierReview?.finalScore).toBe(97); // 4th place
      expect(runnerUps[6].tierReview?.finalScore).toBe(91); // 10th place
    });

    it('should handle podium ordering correctly for visual layout', () => {
      const mockGames = [
        createMockGame('First Place', 100),
        createMockGame('Second Place', 90),
        createMockGame('Third Place', 80),
      ];

      const sortedGames = mockGames
        .filter(
          game => game.tierReview?.finalScore !== null && game.tierReview?.finalScore !== undefined,
        )
        .sort((a, b) => (b.tierReview?.finalScore || 0) - (a.tierReview?.finalScore || 0));

      const podiumGames = sortedGames.slice(0, 3);

      // Podium visual order: 2nd, 1st, 3rd
      const podiumOrder = [
        podiumGames[1], // 2nd place (left)
        podiumGames[0], // 1st place (center, highest)
        podiumGames[2], // 3rd place (right)
      ].filter(Boolean);

      expect(podiumOrder).toHaveLength(3);
      expect(podiumOrder[0].gameName).toBe('Second Place'); // Left position
      expect(podiumOrder[1].gameName).toBe('First Place'); // Center position
      expect(podiumOrder[2].gameName).toBe('Third Place'); // Right position
    });

    it('should handle cases with fewer than 3 games', () => {
      const mockGames = [createMockGame('Game A', 90), createMockGame('Game B', 80)];

      const sortedGames = mockGames
        .filter(
          game => game.tierReview?.finalScore !== null && game.tierReview?.finalScore !== undefined,
        )
        .sort((a, b) => (b.tierReview?.finalScore || 0) - (a.tierReview?.finalScore || 0));

      const topGames = sortedGames.slice(0, 10);
      const podiumGames = topGames.slice(0, 3);
      const runnerUps = topGames.slice(3, 10);

      expect(topGames).toHaveLength(2);
      expect(podiumGames).toHaveLength(2);
      expect(runnerUps).toHaveLength(0);

      // Podium visual order with missing games
      const podiumOrder = [
        podiumGames[1], // 2nd place (left) - undefined
        podiumGames[0], // 1st place (center)
        podiumGames[2], // 3rd place (right) - undefined
      ].filter(Boolean);

      expect(podiumOrder).toHaveLength(2);
      expect(podiumOrder[0].gameName).toBe('Game B'); // 2nd place (left position)
      expect(podiumOrder[1].gameName).toBe('Game A'); // 1st place (center position)
    });
  });
});
