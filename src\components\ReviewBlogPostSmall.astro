---
import * as _ from 'lodash-es';
import type { BlogGameResult } from '@/server/routers/blog';
import { getScoreColor, getGameScoreboard } from '@/helpers/scores';

interface Props {
  game: BlogGameResult;
}

const { game } = Astro.props;

const gameName = game?.gameName ?? '';
const tierItems = game.tierItems;

const tierItemsToShow = [
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'event')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'hypeness')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'suckz')!,
  tierItems.find(ti => ti.tierLane?.tierSetSlug === 'steam-tag')!,
].filter(ti => !!ti);

const reviewed = (tierItems.find(r => r.reviewTitle) as (typeof tierItems)[number]) ?? tierItems[0];

const reviewTitle = reviewed?.reviewTitle ?? '';
const targetUrl = (reviewed ? `/blog/${game?.slug}` : game?.gameUrl) ?? '#';

const tagText = tierItemsToShow
  .map(review => review.tierLane?.label)
  .filter(Boolean)
  .join(' · ');

// Score data
const scoreboard = getGameScoreboard(game);
const finalScore = scoreboard?.finalScore;
const scoreColor = typeof finalScore === 'number' ? getScoreColor(finalScore) : null;
---

<a
  href={targetUrl}
  rel="prefetch"
  class="block h-full w-full rounded-lg border border-gray-200/80 bg-white/80 p-4 shadow-sm transition-all duration-200 ease-in-out hover:border-gray-300 hover:bg-white hover:shadow-md dark:border-gray-700/50 dark:bg-gray-800/50 dark:text-gray-200 dark:hover:border-gray-600 dark:hover:bg-gray-800/80"
>
  <div class="flex flex-col gap-2">
    <!-- Header with Title and Score -->
    <div class="flex items-start justify-between gap-2">
      <!-- Main Title: Game Name + Review Title -->
      <h3 class="font-bold text-gray-900 dark:text-white">
        <span>{gameName}</span>
        <span class="ml-1 font-normal text-gray-600 dark:text-gray-300">{reviewTitle}</span>
      </h3>

      <!-- Score Display -->
      {
        typeof finalScore === 'number' ?  (
          <div
            class="flex h-8 w-8 flex-shrink-0 cursor-help items-center justify-center rounded-full text-xs font-bold shadow-sm transition-transform hover:scale-110"
            style={`background-color: ${scoreColor}; color: white;`}
            title={
              scoreboard
                ? `Final Score: ${Math.round(finalScore)}/100\n\nScoreboard:\nBias: ${scoreboard.scores.bias ?? 'N/A'}\nCreative: ${scoreboard.scores.creative ?? 'N/A'}\nFun: ${scoreboard.scores.fun ?? 'N/A'}\nPotential: ${scoreboard.scores.potential ?? 'N/A'}\nGraphic: ${scoreboard.scores.graphic ?? 'N/A'}\nUI: ${scoreboard.scores.ui ?? 'N/A'}\nAudio: ${scoreboard.scores.audio ?? 'N/A'}\nControl: ${scoreboard.scores.control ?? 'N/A'}\nStability: ${scoreboard.scores.stability ?? 'N/A'}\nBuy: ${scoreboard.scores.buy ?? 'N/A'}`
                : `Score: ${Math.round(finalScore)}/100`
            }
          >
            {Math.round(finalScore)}
          </div>
        ): <></>
      }
    </div>

    <!-- Tags rendered as a single line of plain text -->
    {tagText && <div class="text-xs font-medium text-(--theme-secondary)">{tagText}</div>}
  </div>
</a>
