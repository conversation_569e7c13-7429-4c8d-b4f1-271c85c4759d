<script setup lang="ts">
import { Icon } from '@iconify/vue';

import { cn } from '@/helpers/cn';
import {
  SCORE_DEFINITIONS,
  calculateFinalScore,
  getDynamicColor,
  getDynamicColorWithAlpha,
  getScoreColor,
} from '@/helpers/scores';
import type { BlogGameResult } from '@/server/routers/blog';
import type { GameSteamWithRating } from '@/stores/item';

const props = withDefaults(
  defineProps<{
    game: BlogGameResult | GameSteamWithRating;
    variant?: 'full' | 'compact';
    columns?: number;
  }>(),
  {
    variant: 'full',
    columns: 5,
  },
);

// Get scores from tierReview
const scores = computed(() => {
  const tierReview = props.game.tierReview;
  if (!tierReview) return null;

  return Object.values(SCORE_DEFINITIONS).map(scoreDef => ({
    ...scoreDef,
    value: tierReview[scoreDef.key] ?? 0,
  }));
});

const finalScore = computed(() => calculateFinalScore(props.game.tierReview));

// Show/hide descriptions state
const showMore = ref(false);

// Toggle descriptions
const toggleShowMore = () => {
  showMore.value = !showMore.value;
};
</script>

<template>
  <div v-if="scores" :class="cn('score-breakdown', variant)">
    <!-- Final Score Display -->
    <div
      :class="
        cn(
          'mb-1 flex items-center justify-center gap-1 text-xl',
          'rounded-xl border p-2 shadow-2xl backdrop-blur-sm',
          'border-gray-700/50 bg-gradient-to-br from-gray-900/80 to-gray-800/80',
        )
      "
      v-tooltip.left="'Final Review Score'"
    >
      <Icon icon="mdi:star" class="text-gray-300" :inline="true" />
      <div class="font-bold" :style="`color: ${getScoreColor(finalScore)};`">
        {{ finalScore ?? '-' }}/100
      </div>

      <!-- Toggle buttons for full variant -->
      <div class="absolute right-0 flex gap-1 p-1">
        <button
          :class="
            cn(
              'rounded-md px-2 py-1 text-xs transition-colors',
              showMore
                ? 'bg-blue-600/50 text-blue-200 hover:bg-blue-500/50'
                : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50',
            )
          "
          @click="toggleShowMore"
        >
          {{ showMore ? 'Hide' : 'Show' }} Info
        </button>
      </div>
    </div>

    <!-- Score Grid -->
    <div
      :class="
        cn(
          'grid gap-1 rounded-xl border p-2 shadow-2xl backdrop-blur-sm',
          'grid-cols-2',
          {
            1: 'grid-cols-1',
            2: 'grid-cols-2',
            3: 'grid-cols-3',
            4: 'grid-cols-4',
            5: 'grid-cols-5',
            6: 'grid-cols-6',
          }[columns || '5'],
          variant === 'full'
            ? 'border-gray-700/50 bg-gradient-to-br from-gray-900/80 to-gray-800/80'
            : 'gap-2 border-gray-700/20 bg-gray-900/40 p-2',
        )
      "
    >
      <!-- Scores -->
      <div
        v-for="score in scores"
        :key="score.key"
        :class="cn('score-item flex flex-col gap-0.5')"
        v-tooltip.top="{
          value: `${score.label} ${score.desc}: ${score.value}/100`,
          pt: {
            text: 'font-medium bg-black/70 text-sm',
          },
        }"
      >
        <div
          :class="
            cn(
              'flex items-center text-center transition-all duration-200',
              variant === 'full'
                ? 'flex-col gap-3 rounded-lg p-3 hover:bg-gray-800/50'
                : 'flex-row gap-1 rounded p-1 hover:bg-gray-800/20',
            )
          "
        >
          <!-- Icon and Label -->
          <div class="flex flex-col items-center gap-1">
            <span
              v-if="variant === 'full'"
              :class="cn('mb-1', variant === 'full' ? 'text-2xl' : 'text-lg')"
            >
              {{ score.icon }}
            </span>
            <span
              :class="cn('font-semibold text-gray-300', variant === 'full' ? 'text-sm' : 'text-xs')"
            >
              {{ score.label }}
            </span>
          </div>

          <!-- Score Bar -->
          <div
            :class="
              cn(
                'relative w-full overflow-hidden rounded-full bg-gray-700 shadow-inner',
                variant === 'full' ? 'h-4' : 'h-2',
              )
            "
          >
            <div
              class="score-bar h-full rounded-full transition-all duration-1000 ease-out"
              :style="{
                width: `${score.value}%`,
                background: `linear-gradient(90deg, ${getDynamicColorWithAlpha(score.color, score.value, 0.4)}, ${getDynamicColor(score.color, score.value)})`,
                boxShadow: `0 0 10px ${getDynamicColorWithAlpha(score.color, score.value, 0.5)}`,
              }"
            ></div>
            <!-- Shine effect for full variant -->
            <div
              v-if="variant === 'full'"
              class="absolute inset-0 rounded-full"
              :style="{
                background:
                  'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%)',
                width: `${score.value}%`,
              }"
            ></div>
          </div>

          <!-- Score Value -->
          <div class="flex flex-col items-center gap-1">
            <span
              :class="
                cn(
                  'rounded-full font-bold shadow-lg',
                  variant === 'full' ? 'px-3 py-1 text-sm' : 'px-2 py-0.5 text-xs',
                )
              "
              :style="{
                background: `linear-gradient(135deg, ${getDynamicColorWithAlpha(score.color, score.value, 0.2)}, ${getDynamicColorWithAlpha(score.color, score.value, 0.4)})`,
                color: getDynamicColor(score.color, score.value),
                border: `1px solid ${getDynamicColorWithAlpha(score.color, score.value, 0.6)}`,
              }"
            >
              {{ score.value }}
            </span>
          </div>
        </div>

        <!-- Description  -->
        <div v-if="showMore" class="line-clamp-2 text-xs text-gray-400">
          <span class="text-xs text-gray-500"> ({{ Math.round(score.weight * 100) }}%) </span>
          {{ score.desc }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.score-bar {
  animation: fillBar 1.5s ease-out;
}

@keyframes fillBar {
  0% {
    width: 0%;
  }
}

/* Hover effects */
.score-item:hover {
  transform: translateY(-2px);
}

.score-item:hover .score-bar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.score-breakdown.compact .score-item:hover {
  transform: translateY(-1px);
}
</style>
