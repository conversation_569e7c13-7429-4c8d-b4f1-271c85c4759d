import { format } from 'date-fns';
import { sql } from 'kysely';
import { z } from 'zod';

import { TRPCError } from '@trpc/server';

import { type KyselyInstance, kysely } from '@/database/kysely';
import { applyComputedTierLane } from '@/database/tierLane';
import { slugifyWithSettings } from '@/helpers/utils';

import { createRouter, publicProcedure } from '../trpc';

const filterSchema = z.object({
  slugs: z.array(z.string()).default([]),
  tierSetSlugs: z.array(z.string()).default([]),
  spaces: z.array(z.number()).default([]),
  sorts: z
    .array(
      z
        .object({
          order: z.union([z.literal(-1), z.literal(1)]).default(1),
        })
        .and(
          z.object({
            type: z.enum(['FIELD']),
            value: z.enum(['label', 'score', 'createdAt', 'updatedAt']),
          }),
        ),
    )
    .default([]),
});

const getTierLaneFullQuery = (
  { slugs = [], tierSetSlugs = [], spaces = [], sorts = [] }: Partial<z.infer<typeof filterSchema>>,
  tx: KyselyInstance,
  userId?: string,
) => {
  let query = tx
    .selectFrom('tierLane as tl')
    .innerJoin('tierSet as ts', 'ts.slug', 'tl.tierSetSlug')
    .selectAll('tl')
    .select(eb => [
      eb
        .exists(
          eb
            .selectFrom('playersOnSpaces as pos')
            .whereRef('pos.spaceId', '=', 'ts.spaceId')
            .innerJoin('player as p', 'p.id', 'pos.playerId')
            .where('p.userId', '=', userId!),
        )
        .as('canEdit'),
    ]);

  if (slugs.length) {
    query = query.where('tl.slug', 'in', slugs);
  }

  if (tierSetSlugs.length) {
    query = query.where('tl.tierSetSlug', 'in', tierSetSlugs);
  }

  if (spaces.length) {
    query = query.where('ts.spaceId', 'in', spaces);
  }

  if (sorts.length) {
    sorts.forEach(sort => {
      if (sort.order) {
        if (sort.type === 'FIELD') {
          query = query.orderBy(
            ({ ref }) => ref(sort.value),
            sql`${sql.raw(sort.order > 0 ? 'asc' : 'desc')} nulls last`,
          );
        }
      }
    });
  }

  return query;
};

export const tierLaneRouter = createRouter({
  // ---------------------------
  retrieve: publicProcedure
    .input(
      z.object({
        slug: z.string(),
      }),
    )
    .query(async ({ input: { slug }, ctx: { user } }) => {
      const fullQuery = getTierLaneFullQuery({ slugs: [slug] }, kysely, user?.id);
      const result = await fullQuery.executeTakeFirst();
      const tierItem = result ? applyComputedTierLane(result) : null;
      return tierItem;
    }),
  // ---------------------------
  retrieveMany: publicProcedure.input(filterSchema).query(async ({ input, ctx: { user } }) => {
    const fullQuery = getTierLaneFullQuery(input, kysely, user?.id);
    const result = await fullQuery.execute();
    const tierLanes = result.map(applyComputedTierLane);
    return tierLanes;
  }),
  // ---------------------------
  create: publicProcedure
    .input(
      z.object({
        tierSetSlug: z.string(),
        slug: z.string().optional(),
        label: z.string().optional(),
        displayName: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const dateStr = format(new Date(), 'yyyy-MM-dd hh:mm:ss');
      const label = input.label ?? input.displayName + ' ' + dateStr;
      const slug = input.slug ?? slugifyWithSettings(input.displayName + ' ' + dateStr);

      const query = kysely
        .insertInto('tierLane')
        .values({
          slug,
          label,
          tierSetSlug: input.tierSetSlug,
        })
        .returningAll();

      const result = await query.executeTakeFirst();

      const tierLane = result ? applyComputedTierLane(result) : null;

      return tierLane;
    }),
  // ---------------------------
  update: publicProcedure
    .input(
      z.object({
        tierLaneSlug: z.string(), // old slug
        tierSetSlug: z.string(),
        label: z.string().optional(),
        slug: z.string().optional(),
        displayName: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const { tierLaneSlug: oldSlug, slug, ...data } = input;
      const newTierLaneSlug = slug || oldSlug;

      await kysely.transaction().execute(async trx => {
        const updateQuery = trx
          .updateTable('tierLane as tl')
          .where('tl.slug', '=', oldSlug)
          .set(data);

        const updateResult = await updateQuery.executeTakeFirst();
        console.log('updateResult', updateResult);
        if ((updateResult.numChangedRows ?? 0) == 0 && updateResult.numUpdatedRows == 0n) {
          const insertQuery = trx.insertInto('tierLane').values({
            ...data,
            slug: newTierLaneSlug,
          });
          const insertResult = await insertQuery.executeTakeFirst();
          console.log('insertResult', insertResult);
        }
      });

      const fullQuery = getTierLaneFullQuery({ slugs: [newTierLaneSlug] }, kysely, user?.id);
      const result = await fullQuery.executeTakeFirst();

      const tierLane = result ? applyComputedTierLane(result) : null;

      return tierLane;
    }),
  // ---------------------------
  updateScore: publicProcedure
    .input(
      z.object({
        slug: z.string(),
        score: z.number(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const { slug, ...data } = input;

      const updateQuery = kysely
        .updateTable('tierLane as tl')
        .where('tl.slug', '=', slug)
        .set(data)
        .returningAll();

      const updateResult = await updateQuery.executeTakeFirst();

      const tierLane = updateResult ? applyComputedTierLane(updateResult) : null;

      return tierLane;
    }),
  // ---------------------------
  delete: publicProcedure
    .input(
      z.object({
        tierLaneSlug: z.string(),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      if (!user) {
        throw new TRPCError({ code: 'UNAUTHORIZED' });
      }

      const { tierLaneSlug } = input;

      const query = kysely
        .deleteFrom('tierLane as tl')
        .where('tl.slug', '=', tierLaneSlug)
        .returningAll();

      const result = await query.executeTakeFirst();

      const tierLane = result ? applyComputedTierLane(result) : null;

      return tierLane;
    }),
});

export type TierLaneRouter = typeof tierLaneRouter;
