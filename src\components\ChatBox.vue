<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { MilkdownProvider } from '@milkdown/vue';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/vue';

import LogoFlat from '@/assets/logo-flat.svg';
import { pinia, useUserStore } from '@/stores/user';

import MarkdownEditor from './MarkdownEditor.vue';

const props = defineProps({
  tierItems: {
    type: Array as () => Types.TierItem[],
    default: () => [],
  },
});

const userStore = useUserStore(pinia);
const { avatarUrl } = storeToRefs(userStore);

type ChatObj = {
  sender: string;
  content: string;
} & (
  | {
      type: 'chat';
    }
  | {
      type: 'review';
      review: Types.TierItem;
    }
  | {
      type: 'tierset';
    }
  | {
      type: 'search';
    }
);

const chatObjs = ref<ChatObj[]>([
  ...props.tierItems.map(
    review =>
      ({
        sender: 'demoman',
        type: 'review',
        review: review,
        content: 'New Review',
      }) as ChatObj,
  ),
  {
    sender: 'user',
    type: 'search',
    content: 'Lorem ipsum dolor sit amet, consectetur adipisicing elit.Lorem ipsum dolor sit amet.',
  },
]);
</script>

<template>
  <!-- chat box -->
  <div class="flex h-96 w-full flex-col rounded-md border bg-gray-800/50 shadow-md">
    <div class="flex-1 overflow-y-auto px-4 py-4">
      <!-- chat message -->
      <div
        v-for="(chat, i) in chatObjs"
        :key="i"
        :class="['mb-4 flex items-center', chat.sender === 'user' ? 'flex-row-reverse' : '']"
      >
        <div
          :class="[
            'flex flex-none flex-col items-center space-y-1',
            chat.sender === 'user' ? 'ml-4' : 'mr-4',
          ]"
        >
          <img
            :src="chat.sender === 'user' ? avatarUrl : LogoFlat.src"
            class="h-10 w-10 rounded-full"
            alt="sender"
            referrerpolicy="no-referrer"
          />
        </div>
        <div
          :class="[
            'relative mb-2 w-fit max-w-[50%] min-w-0 flex-1 rounded-lg p-2',
            chat.sender === 'user'
              ? 'bg-theme-secondary text-gray-800'
              : 'bg-theme-primary text-white',
          ]"
        >
          <div v-if="chat.content">{{ chat.content }}</div>
          <div class="h-fit max-h-80 w-full overflow-auto">
            <div v-if="chat.type === 'review'">
              <div class="flex h-full w-full">
                <div>
                  <img :src="chat.review.displayImage!" alt="poster" />
                </div>
                <div class="h-full w-full flex-row">
                  <div>{{ chat.review.reviewTitle }}</div>
                  <MilkdownProvider>
                    <ProsemirrorAdapterProvider>
                      <MarkdownEditor
                        :model-value="chat.review.reviewContent ?? ''"
                        :disabled="true"
                        class="prose m-1 h-full w-full overflow-auto rounded-xl bg-slate-300 p-3 shadow-2xl"
                      />
                    </ProsemirrorAdapterProvider>
                  </MilkdownProvider>
                </div>
              </div>
            </div>
          </div>

          <!-- arrow -->
          <div
            v-if="chat.sender === 'user'"
            class="bg-theme-secondary absolute top-1/2 right-0 h-2 w-2 translate-x-1/2 rotate-45 transform"
          ></div>
          <div
            v-else
            class="bg-theme-primary absolute top-1/2 left-0 h-2 w-2 -translate-x-1/2 rotate-45 transform"
          ></div>
          <!-- end arrow -->
        </div>
      </div>
      <!-- end chat message -->
    </div>

    <div class="flex items-center border-t p-2">
      <!-- chat input action -->
      <div>
        <button
          class="hover:bg-theme-text hover:text-theme-bg inline-flex rounded-md p-2"
          type="button"
        >
          <Icon icon="lucide:menu"></Icon>
        </button>
      </div>
      <!-- end chat input action -->

      <div class="w-full">
        <input
          class="w-full rounded-md p-1"
          type="text"
          value=""
          placeholder="Ask anything"
          autofocus
        />
      </div>

      <!-- chat send action -->

      <div>
        <button
          class="hover:bg-theme-text hover:text-theme-bg inline-flex rounded-md p-2"
          type="button"
        >
          <Icon icon="lucide:send"></Icon>
        </button>
      </div>

      <!-- end chat send action -->
    </div>
  </div>

  <!-- end chat box -->
</template>
