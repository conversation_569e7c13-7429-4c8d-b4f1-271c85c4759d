import type { APIContext, GetStaticPathsResult } from 'astro';
import { sql } from 'kysely';
import { jsonArrayFrom } from 'kysely/helpers/postgres';

import { Resvg } from '@resvg/resvg-js';

import type { ComputedPropertiesOfGameSteam } from '@/database/gameSteam';
import { type KyselyDatabase, kysely } from '@/database/kysely';
import { applyComputedSpace } from '@/database/space';
import { getSupabaseServerClient } from '@/database/supabase.server';
import { getTierImageStr } from '@/helpers/images/tier-image-utils';

export const prerender = false;

const query = kysely.selectFrom('space as sp').select(['slug', 'label']);

export async function getStaticPaths(): Promise<GetStaticPathsResult> {
  const spaces = await query.execute();

  return spaces.map(space => ({
    params: { spaceSlug: space.slug! },
  }));
}

type Props = {};

type Params = {
  spaceSlug: string;
};

export async function GET({ params, request, cookies }: APIContext<Props, Params>) {
  const filePath = `og-image/spaces/${params.spaceSlug}.png`;

  const supabase = getSupabaseServerClient(request.headers.get('Cookie'), cookies);
  // const {
  //   data: { publicUrl },
  // } = supabase.storage.from('images').getPublicUrl(filePath);

  // try {
  //   const responseBefore = await fetch(publicUrl, { method: 'HEAD' });
  //   if (responseBefore.status == 200) {
  //     return redirect(publicUrl);
  //   }
  // } catch {
  //   // Proceed to create image
  // }

  const result = await query
    .innerJoin('tierSet as ts', 'ts.spaceId', 'sp.id')
    .innerJoin('tierLane as tl', 'tl.tierSetSlug', 'ts.slug')
    .select(eb =>
      jsonArrayFrom(
        eb
          .selectFrom('tierItem as ti')
          .whereRef('ti.tierLaneSlug', '=', 'tl.slug')
          .innerJoin('gameSteam as gs', 'gs.steamId', 'ti.gameSteamId')
          .where(eb2 =>
            eb2.or([
              eb2('gs.hasImageLibraryPoster', '=', true),
              eb2('gs.hasImageHeroCapsule', '=', true),
            ]),
          )
          .selectAll('ti')
          .select(sql<KyselyDatabase['gameSteam']>`to_json(gs.*)`.as('gameSteam'))
          .limit(50),
      ).as('tierItems'),
    )
    .executeTakeFirst();

  if (!result) {
    return new Response(null, {
      status: 404,
      statusText: 'Not found',
    });
  }

  const space = applyComputedSpace(result);

  const svg = await getTierImageStr({
    title: space.label ?? '',
    images: space.tierItems.map(
      item => (item.gameSteam as unknown as ComputedPropertiesOfGameSteam)?.imageFinalTall!,
    ),
  });
  const png = new Resvg(svg).render().asPng();

  supabase.storage
    .from('images')
    .upload(filePath, png)
    .then(({ error }) => {
      if (error) {
        console.error(error);
      }
    });

  return new Response(png, {
    headers: { 'Content-Type': 'image/png' },
  });
}
