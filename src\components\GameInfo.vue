<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { MilkdownProvider } from '@milkdown/vue';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/vue';
import { watchDebounced, watchIgnorable } from '@vueuse/core';

import TierItemLinks from '@/components/TierItemLinks.vue';
import { type TierItemForMainPage } from '@/database/tierItem';
import { trpc } from '@/services/trpc';
import { pinia, useItemStore } from '@/stores/item';
import { useUserStore } from '@/stores/user';

import GameTag from './GameTag.vue';
import MarkdownEditor from './MarkdownEditor.vue';
import TagLink from './TagLink.vue';

const emit = defineEmits(['update:tierItem']);

const userStore = useUserStore(pinia);
const itemStore = useItemStore(pinia);
const { isDemomanSpace } = storeToRefs(userStore);
const { schema, displayingTierItem, canEditDisplayingTierItem } = storeToRefs(itemStore);

type APIResponseTierItem = Awaited<ReturnType<typeof trpc.tierItem.retrieve.query>>;
const infoCache = ref<Record<string, APIResponseTierItem>>({});

const isEditingTitle = ref(false);
const isLoadingUpdate = ref(false);
const tierItemInfo = ref<APIResponseTierItem | null>(null);
const gameInfo = ref<Exclude<APIResponseTierItem, null>['gameSteam'] | null>();
const reviewTitle = ref('');
const reviewContent = ref('');
const publishDate = ref<Date>();
const hasNewReviewContentUpdate = ref(false);

function setLocalInfos(item: APIResponseTierItem | null) {
  ignoreReviewContentUpdate(() => {
    tierItemInfo.value = item;
    reviewTitle.value = item?.reviewTitle ?? '';
    reviewContent.value = item?.reviewContent ?? '';
    gameInfo.value = item?.gameSteam ?? null;
    publishDate.value = item?.publishDate ?? undefined;
  });
}

async function getTierItemInfo() {
  if (tierItemInfo.value) {
    return;
  }
  try {
    console.log('getTierItemInfo:', displayingTierItem.value);
    const loadedItem = infoCache.value[displayingTierItem.value?.id!];
    if (loadedItem) {
      console.log('already loaded:', loadedItem);
      setLocalInfos(loadedItem);
    }

    const tierItem = await trpc.tierItem.retrieve.query({
      id: displayingTierItem.value!.id,
    });
    if (tierItem) {
      console.log('got data from server:', tierItem);
      infoCache.value[tierItem.id] = tierItem;
      setLocalInfos(tierItem);
    }
  } catch (err) {
    console.error(err);
  }
}

async function createTierItems<T extends Pick<TierItemForMainPage, 'id' | 'tierSetSlug' | 'type'>>(
  tierItems: T[],
) {
  const createdItems = await trpc.tierItem.createMany.mutate({
    items: tierItems,
  });
  return createdItems;
}

async function updateTierItem(payload: {
  tierSetSlug: string;
  id: number;
  reviewTitle?: string;
  reviewContent?: string;
  publishDate?: Date;
  tierLaneSlug?: string;
}) {
  if (!tierItemInfo.value) {
    return;
  }
  isLoadingUpdate.value = true;
  const updated = await trpc.tierItem.update.mutate({
    ...payload,
  });
  tierItemInfo.value = {
    ...tierItemInfo.value,
    ...(updated as any),
  };
  if (displayingTierItem.value) {
    displayingTierItem.value = {
      ...displayingTierItem.value,
      fullTitle: updated!.fullTitle,
      tierLaneSlug: updated!.tierLaneSlug,
      publishDateFormatted: updated!.publishDateFormatted,
    };
  }

  isLoadingUpdate.value = false;
}

async function selectTierLaneSlug(i: number, j: number) {
  if (!displayingTierItem.value || !tierItemInfo.value) {
    return;
  }

  const oldSetSlug = displayingTierItem.value?.tierSetSlug;
  const oldLaneSlug = displayingTierItem.value?.tierLaneSlug;
  const oldSetI = schema.value.findIndex(set => set.slug === oldSetSlug);
  const oldLaneI = schema.value[oldSetI].tierLanes.findIndex(lane => lane.slug === oldLaneSlug);
  const oldLane = schema.value[oldSetI].tierLanes[oldLaneI];
  const newSet = schema.value[i];
  const newLane = newSet.tierLanes[j];
  const oldIndex = oldLane.tierItems.findIndex(item => item.id === displayingTierItem.value?.id);
  const nextSel = `[data-item-id="${displayingTierItem.value?.id}"] ~ :not(.hidden)`;
  const nextEl: HTMLElement | null = document.querySelector(nextSel);
  const nextItem = oldLane.tierItems.find(item => item.id === +nextEl?.dataset?.['itemId']!);

  if (oldLane === newLane) {
    return;
  }

  if (oldSetSlug !== newSet.slug) {
    const [item] = await createTierItems([
      {
        ...displayingTierItem.value,
        tierSetSlug: newSet.slug,
        tierLaneSlug: newLane.slug,
      },
    ]);
    schema.value[i].tierLanes[j].tierItems.push(item);
  } else {
    updateTierItem({
      tierSetSlug: displayingTierItem.value.tierSetSlug,
      id: displayingTierItem.value.id,
      tierLaneSlug: newLane.slug,
    });
    const [item] = schema.value[i].tierLanes[oldLaneI].tierItems.splice(oldIndex, 1);
    item.tierLaneSlug = newLane.slug;
    schema.value[i].tierLanes[j].tierItems.push(item);
  }

  schema.value[i].tierLanes = [...schema.value[i].tierLanes];

  displayingTierItem.value = nextItem as TierItemForMainPage;
}

async function confirmUpdateTitle() {
  if (!displayingTierItem.value || !tierItemInfo.value) {
    return;
  }
  await updateTierItem({
    tierSetSlug: displayingTierItem.value.tierSetSlug,
    id: displayingTierItem.value.id,
    reviewTitle: reviewTitle.value,
  });
  isEditingTitle.value = false;
}

function cancelUpdateTitle() {
  reviewTitle.value = tierItemInfo.value?.reviewTitle ?? '';
  isEditingTitle.value = false;
}

function closeTierItemModal() {
  displayingTierItem.value = undefined;
}

watch(displayingTierItem, (val, oldVal) => {
  tierItemInfo.value = null;
  gameInfo.value = null;
  if (val && val?.id !== oldVal?.id) {
    isLoadingUpdate.value = false;
    isEditingTitle.value = false;

    getTierItemInfo();
  }
});

const { ignoreUpdates: ignoreReviewContentUpdate } = watchIgnorable(
  [reviewContent, publishDate],
  () => {
    hasNewReviewContentUpdate.value = true;
  },
);

watchDebounced(
  hasNewReviewContentUpdate,
  (val, oldVal) => {
    if (!val || oldVal) {
      return;
    }
    if (!displayingTierItem.value || !tierItemInfo.value) {
      return;
    }
    updateTierItem({
      tierSetSlug: displayingTierItem.value.tierSetSlug,
      id: displayingTierItem.value.id,
      reviewContent: reviewContent.value,
      publishDate: publishDate.value,
    });
    hasNewReviewContentUpdate.value = false;
  },
  { debounce: 10_000 },
);

watch(displayingTierItem, (newVal, oldVal) => {
  if (!hasNewReviewContentUpdate.value) {
    return;
  }
  if (oldVal && oldVal.id !== newVal?.id) {
    updateTierItem({
      tierSetSlug: oldVal.tierSetSlug,
      id: oldVal.id,
      reviewContent: reviewContent.value,
      publishDate: publishDate.value,
    });
    hasNewReviewContentUpdate.value = false;
  }
});
</script>

<template>
  <div
    :class="[
      'ignore-drag',
      'fixed inset-0 top-0 left-0 z-50 flex h-screen w-screen',
      'items-center justify-center bg-cover bg-center bg-no-repeat',
      'animated fadeIn faster outline-hidden focus:outline-hidden',
    ]"
    v-if="displayingTierItem"
  >
    <div
      class="absolute inset-0 z-0 h-screen bg-black opacity-90"
      :style="{
        backgroundImage: displayingTierItem.gameSteam
          ? `url(${displayingTierItem.gameSteam?.imagePageBgBlur!})`
          : undefined,
      }"
      @click="closeTierItemModal"
    ></div>
    <div
      :class="[
        'absolute inset-0 -z-1 h-screen w-full',
        'background-animate bg-linear-to-r',
        'from-pink-500 via-red-500 to-yellow-500 opacity-30',
      ]"
      @click="closeTierItemModal"
    ></div>
    <div :class="['relative h-full w-full rounded-xl']" @click="closeTierItemModal">
      <div
        :class="[
          'absolute top-0 right-0 z-10 m-0.5 rounded-md p-1 opacity-70',
          'cursor-pointer hover:bg-red-600 hover:opacity-100 hover:ring-2 hover:ring-red-700',
        ]"
      >
        <a class="" @click="closeTierItemModal">
          <Icon icon="lucide:x"></Icon>
        </a>
      </div>
      <div
        class="my-auto flex h-full flex-wrap items-center justify-center rounded-md p-1 sm:mx-10"
      >
        <div
          class="sm:max-h-unset flex max-h-[50vh] w-full min-w-0 grow flex-col justify-center p-1 sm:w-1/2"
          @click.stop
        >
          <video
            v-if="gameInfo?.movies?.[0]?.mp4?.[480]"
            :src="gameInfo.movies?.[0]?.mp4?.[480]"
            autoplay
            controls
            class="w-full rounded-md shadow-2xl"
          >
            <track label="English" kind="captions" srclang="en" default />
          </video>
          <template v-else>
            <img
              :src="displayingTierItem.displayImage"
              alt="Preview Image"
              class="sm:max-h-unset max-h-[50vh] max-w-full rounded-md shadow-2xl"
            />
          </template>
          <div class="relative flex flex-wrap gap-1">
            <div v-for="(mv, i) in gameInfo?.movies ?? []" class="w-16">
              <!-- <img
                :src="mv.thumbnail"
                :alt="`Trailer ${i + 1}: ${mv.name}`"
                :class="['rounded-md shadow-2xl', 'hover:w-80 hover:absolute']"
              /> -->
              <video
                v-if="mv.teaser"
                :src="mv.teaser"
                autoplay
                loop
                :class="['rounded-md shadow-2xl', 'hover:absolute hover:w-80']"
              >
                <track label="English" kind="captions" srclang="en" default />
              </video>
            </div>
            <div v-for="(ss, i) in gameInfo?.screenshots ?? []" class="w-16">
              <img
                :src="ss?.path_full"
                :alt="`Screenshot ${i + 1}`"
                :class="['rounded-md shadow-2xl', 'hover:absolute hover:w-80']"
              />
            </div>
          </div>
        </div>
        <div
          :class="[
            'flex w-full max-w-full flex-col gap-1 sm:w-[65ch] sm:max-w-[65ch]',
            'justify-center p-1 text-white',
            tierItemInfo ? 'h-full' : '',
          ]"
          @click.stop
        >
          <!-- Showing -->
          <template v-if="tierItemInfo">
            <div
              v-if="!isEditingTitle"
              :class="[
                'group/title flex items-center',
                tierItemInfo ? 'justify-start' : 'animate-ping justify-center',
              ]"
            >
              <span class="p-1 font-black">{{ tierItemInfo.fullTitle || '-' }}</span>
              <TierItemLinks :tierItem="tierItemInfo"></TierItemLinks>
              <div
                v-if="canEditDisplayingTierItem"
                :class="[
                  'cursor-pointer text-sm text-(--text)',
                  'opacity-0 transition-opacity group-hover/title:opacity-70',
                ]"
                @click.stop="isEditingTitle = true"
              >
                <Icon icon="lucide:settings" />
              </div>
            </div>
            <!-- Editing -->
            <div v-else class="ignore-drag flex items-center justify-start gap-1 p-1 text-xs">
              <div class="relative">
                <Icon icon="lucide:text" class="absolute my-2 mr-2 ml-2 text-xs text-(--main)" />
                <PrimeInputText
                  v-model="reviewTitle"
                  inputId="tier-item-label"
                  :placeholder="tierItemInfo.reviewTitle"
                  :class="[
                    'bg-(--text)! py-1! pl-6! text-xs! text-(--main)!',
                    'placeholder:text-(--main)! placeholder:opacity-30',
                  ]"
                />
              </div>

              <PrimeButton
                class="text-theme-text! justify-center bg-green-600! text-xs!"
                @click="confirmUpdateTitle"
                :loading="isLoadingUpdate"
              >
                <Icon icon="lucide:save" />
              </PrimeButton>
              <PrimeButton
                class="text-theme-text! justify-center bg-red-500! text-xs!"
                @click="cancelUpdateTitle"
              >
                <Icon icon="lucide:x" />
              </PrimeButton>
            </div>

            <template v-if="gameInfo">
              <div class="max-h-48 w-full overflow-auto">
                <GameTag v-for="tag in gameInfo.gameTags" :key="tag">{{ tag }}</GameTag>
              </div>
              <div class="max-h-48 w-full rounded-md p-1">
                {{ gameInfo.shortDescription }}
              </div>
            </template>

            <!-- <div class="flex flex-wrap gap-1 m-1">
              <div
                v-for="(tierSet, i) in schema.toReversed()"
                :key="tierSet.slug"
                class="flex flex-wrap gap-1 m-1"
              >
                <div v-for="(lane, j) in tierSet.tierLanes" :key="lane.slug">
                  <PrimeButton
                    @click="selectTierLaneSlug(i, j)"
                    :loading="isLoadingUpdate"
                    :disabled="!canEditDisplayingTierItem"
                    :style="{ '--lane-main': lane.mainColor, '--lane-text': lane.textColor }"
                    :class="[
                      'w-full h-full min-w-12 opacity-60 text-center overflow-hidden',
                      'bg-(--lane-main)! text-(--lane-text)!',
                      'hover:opacity-100 hover:bg-(--lane-text)! hover:text-(--lane-main)!',
                      '',
                      displayingTierItem.tierLaneSlug === lane.slug
                        ? 'opacity-100! outline! outline-2! outline-offset-2! outline-(--lane-text)!'
                        : '',
                    ]"
                  >
                    {{ lane.icon }} {{ lane.label }}
                  </PrimeButton>
                </div>
              </div>
            </div> -->

            <div v-if="isDemomanSpace" class="m-1 flex items-center gap-1">
              <label for="publishDate">Publish Date</label>
              <PrimeCalendar v-model="publishDate" dateFormat="yy-mm-dd" inputId="publishDate" />
            </div>

            <div class="h-full w-full overflow-auto">
              <MilkdownProvider>
                <ProsemirrorAdapterProvider>
                  <MarkdownEditor
                    v-model="reviewContent"
                    :disabled="!canEditDisplayingTierItem"
                    class="prose m-1 h-fit rounded-xl bg-slate-300 p-3 shadow-2xl"
                  />
                </ProsemirrorAdapterProvider>
              </MilkdownProvider>
              <template v-if="gameInfo">
                <div
                  v-for="item in gameInfo.tierItems?.filter(
                    item => item.tierLane?.tierSetSlug !== displayingTierItem?.tierSetSlug,
                  ) ?? []"
                  class="relative m-1 rounded-xl bg-slate-400 p-3 shadow-2xl"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <span class="mr-1">{{ item.tierSet.label }}: </span>
                      <TagLink :tierLane="item.tierLane!"></TagLink>
                    </div>
                    <div>{{ item.reviewTitle }}</div>
                    <div>by {{ item.tierSet.space?.label }}</div>
                  </div>
                  <MilkdownProvider v-if="item.reviewContent">
                    <ProsemirrorAdapterProvider>
                      <MarkdownEditor
                        :modelValue="item.reviewContent"
                        :disabled="true"
                        class="prose h-full w-full"
                      />
                    </ProsemirrorAdapterProvider>
                  </MilkdownProvider>
                </div>
              </template>
            </div>
          </template>
          <template v-else>
            <div class="flex w-full justify-center text-9xl">
              <Icon icon="mdi:gamepad-variant" class="animate-spin"></Icon>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.background-animate {
  background-size: 400%;

  -webkit-animation: BackgroundAnimate 10s ease infinite;
  -moz-animation: BackgroundAnimate 10s ease infinite;
  animation: BackgroundAnimate 10s ease infinite;
}
@keyframes BackgroundAnimate {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
</style>
