---
// import { MENU_LINKS } from "@/data/constants";

const year = new Date().getFullYear();
---

<footer
  id="main-footer"
  class="mx-auto mt-auto flex w-full max-w-3xl flex-col items-center justify-center gap-y-2 pt-20 pb-4 text-center align-top text-gray-500 sm:flex-row sm:justify-between sm:text-xs"
>
  <div class="me-0 sm:me-4">
    Copyright &copy; {year}
    <span aria-label="trophy emoji">🏆</span>
    The Demoman Project by <a href="https://youtube.com/@-yay">@-yay</a>
  </div>
  <!-- <nav
		aria-label="More on this site"
		class="flex gap-x-2 sm:gap-x-0 sm:divide-x sm:divide-gray-500"
	>
		{
			MENU_LINKS.map((link) => (
				<a
					href={link.path}
					class="px-4 py-2 sm:px-2 sm:py-0 sm:hover:text-theme-text sm:hover:underline"
				>
					{link.title}
				</a>
			))
		}
	</nav> -->
</footer>
