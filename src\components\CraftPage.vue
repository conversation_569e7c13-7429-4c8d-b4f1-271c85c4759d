<script setup lang="ts">
import { cn } from '@/helpers/cn';
import type { CraftedItem } from '@/server/routers/craft';
import { trpc } from '@/services/trpc';

const craftingGrid = ref(['', '', '', '', '', '', '', '', '']);
const craftedItem = ref<CraftedItem | null>(null);
const isLoading = ref(false);
const error = ref('');
const canCraft = computed(() => craftingGrid.value.some(item => item.trim()));
const temperature = ref(0.4); // ค่าเริ่มต้นสำหรับ Temperature

// Computed property เพื่อเปลี่ยน Emoji ตามค่า Temperature
const temperatureEmoji = computed(() => {
  if (temperature.value <= 0.2) return '🥶'; // เยือกแข็ง, ผลลัพธ์คาดเดาง่าย
  if (temperature.value <= 0.5) return '🤔'; // กำลังดี, สมดุล
  if (temperature.value <= 0.8) return '🤪'; // สร้างสรรค์, เริ่มแปลก
  return '🔥'; // เดือดสุด, คาดเดายากมาก
});

const handleCraft = async () => {
  isLoading.value = true;
  craftedItem.value = null;
  error.value = '';

  try {
    const result = await trpc.craft.request.query({
      items: craftingGrid.value.map(item => item.trim()),
      // ส่งค่า temperature ไปกับ request
      temperature: +temperature.value,
    });

    if (result.result_item) {
      craftedItem.value = result;
    }
  } catch (e) {
    console.error('Crafting failed:', e);
    error.value = 'คราฟไม่สำเร็จ! ลองใหม่นะ';
  } finally {
    isLoading.value = false;
  }
};

// ฟังก์ชันสำหรับล้างไอเทมทั้งหมดในตาราง
const handleClear = () => {
  craftingGrid.value.fill('');
  craftedItem.value = null;
  error.value = '';
};

/**
 * Calculates a dynamic font size based on the length of the text.
 * @param text The input text.
 * @returns A font size string in pixels (e.g., '24px').
 */
const calculateFontSize = (text: string): string => {
  const textLength = text?.length || 0;
  const MAX_FONT_PX = 40; // Max font size in pixels for short text
  const MIN_FONT_PX = 20; // Min font size in pixels for long text
  const START_SHRINK_AT = 4; // Start shrinking font size after this many characters
  const MIN_FONT_AT = 15; // Font size reaches minimum at this many characters

  if (textLength <= START_SHRINK_AT) {
    return `${MAX_FONT_PX}px`;
  }

  if (textLength >= MIN_FONT_AT) {
    return `${MIN_FONT_PX}px`;
  }

  // Calculate the font size linearly between the max and min points
  const pixelRange = MAX_FONT_PX - MIN_FONT_PX;
  const charRange = MIN_FONT_AT - START_SHRINK_AT;
  const pixelsToShrink = (textLength - START_SHRINK_AT) * (pixelRange / charRange);
  const newSize = MAX_FONT_PX - pixelsToShrink;

  return `${Math.round(newSize)}px`;
};
</script>

<template>
  <div id="crafting-page">
    <div class="mc-container mb-8 w-full max-w-2xl text-center">
      <h1 class="mb-2 text-xl text-gray-800 md:text-2xl">โต๊ะคราฟสุดจินตนาการ</h1>
      <p class="text-gray-700">ใส่ไอเดียของคุณลงไป แล้วดูว่าจะได้อะไรออกมา!</p>
    </div>

    <main class="flex flex-col items-center justify-center gap-8 md:flex-row">
      <!-- Left Crafting Grid -->
      <div class="mc-container w-full md:w-[45%]">
        <div class="grid grid-cols-3 gap-2">
          <div v-for="(item, index) in craftingGrid" :key="index" class="mc-slot">
            <textarea
              v-model="craftingGrid[index]"
              placeholder=""
              :style="{ fontSize: calculateFontSize(craftingGrid[index]) }"
            />
          </div>
        </div>
      </div>

      <!-- Arrow and Button -->
      <div class="flex w-full flex-col items-center gap-4 md:w-[15%]">
        <div class="transform text-5xl text-gray-800 md:transform-none">➔</div>

        <!-- Craft Button -->
        <button @click="handleCraft" :disabled="isLoading || !canCraft" class="mc-button text-2xl">
          <div :class="cn(isLoading ? 'animate-spin' : '')">
            {{ isLoading ? '⏳' : '⚒️' }}
          </div>
        </button>

        <div
          :class="
            cn(
              'flex flex-col items-center justify-center gap-4 transition-opacity',
              isLoading || !canCraft ? 'opacity-0' : '',
            )
          "
        >
          <!-- Temperature Slider -->
          <div class="mt-4 flex w-full flex-col items-center justify-center gap-2 text-gray-800">
            <label class="text-sm text-gray-300">
              ความกาว ({{ temperatureEmoji }} {{ temperature }})
            </label>
            <div class="flex w-full items-center gap-2">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                v-model="temperature"
                class="mc-slider grow"
              />
            </div>
          </div>

          <!-- Clear Button -->
          <button @click="handleClear" class="mc-button-clear text-lg">🗑️</button>
        </div>
      </div>

      <!-- Right Section -->
      <div class="flex w-full flex-col items-center justify-center gap-4 md:w-[40%]">
        <!-- Result Slot -->
        <div class="mc-container w-fit">
          <div
            class="mc-slot flex h-32 w-32 items-center justify-center text-center"
            :style="{ backgroundColor: craftedItem ? craftedItem.color : '#8B8B8B' }"
          >
            <div v-if="isLoading" class="flex h-full items-center justify-center">
              <div class="loader"></div>
            </div>
            <div v-else-if="error" class="text-lg font-bold text-red-600">❌</div>
            <div
              v-else-if="craftedItem"
              class="flex h-full flex-col items-center justify-center text-white"
            >
              <div class="text-center text-4xl tracking-[0em]">{{ craftedItem.emojis }}</div>
            </div>
            <div v-else class="text-gray-300"></div>
          </div>
        </div>

        <!-- Details Box -->
        <div
          v-if="craftedItem || error"
          class="mc-container h-[200px] w-full max-w-sm overflow-auto"
        >
          <div v-if="craftedItem && !isLoading && !error">
            <h2 class="text-xl text-gray-800">{{ craftedItem.result_item }}</h2>
            <hr class="my-2 border-t-2 border-gray-500" />
            <div class="text-left text-gray-700">
              <p class="mb-2">
                {{ craftedItem.description }}
              </p>
              <p class="text-sm italic">(<strong>เหตุผล:</strong> {{ craftedItem.reason }})</p>
            </div>
          </div>
          <div v-else-if="error" class="flex h-full items-center justify-center">
            <p class="text-center text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scss="scss" scoped>
@reference '@/styles/global.css';

#crafting-page {
  @apply flex flex-col items-center justify-center rounded-md p-4;
  background-color: #7f7f7f;
  color: #efefef;
}
.mc-container {
  background-color: #c6c6c6;
  border: 4px solid;
  border-color: #555555 #ffffff #ffffff #555555;
  padding: 1rem;
  image-rendering: pixelated;
}
.mc-slot {
  @apply aspect-1 w-full;
  background-color: #8f8f8f;
  border: 2px solid;
  border-color: #373737 #ffffff #ffffff #373737;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}
.mc-slot textarea {
  @apply content-center text-center;
  @apply h-full w-full p-0.5;
  @apply border-none bg-transparent text-white;
}
.mc-slot textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px #ffffaa;
}

.mc-button {
  background-color: #c6c6c6;
  border: 4px solid;
  border-color: #ffffff #555555 #555555 #ffffff;
  padding: 0.75rem 1.5rem;
  color: #4a4a4a;
  text-shadow: 1px 1px #ffffff;
  transition: all 0.1s;
}
.mc-button:disabled {
  background-color: #e0e0e0bd;
  border-color: #e0e0e0bd;
  opacity: 0.5;
  cursor: not-allowed;
}
.mc-button:not(:disabled) {
  @apply animate-glow shadow-green-400;
}
.mc-button:hover:not(:disabled) {
  background-color: #d1d1d1;
}
.mc-button:active:not(:disabled) {
  border-color: #555555 #ffffff #ffffff #555555;
  background-color: #bdbdbd;
}

.mc-button-clear {
  background-color: #f005;
  border: 2px solid;
  border-color: #ffffff #8b4513 #8b4513 #ffffff;
}
.mc-button-clear:hover {
  background-color: #f00f;
}

/* Custom Slider Styles */
.mc-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 12px;
  background: #8b8b8b;
  border: 2px solid #373737;
  outline: none;
}

.mc-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #c6c6c6;
  border: 2px solid #373737;
  cursor: pointer;
}

.mc-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #c6c6c6;
  border: 2px solid #373737;
  cursor: pointer;
}

/* Loading animation */
.loader {
  width: 48px;
  height: 48px;
  border: 5px solid #fff;
  border-bottom-color: #ffc107;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
