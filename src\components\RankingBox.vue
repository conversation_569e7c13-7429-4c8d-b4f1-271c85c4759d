<script setup lang="ts">
import TierItemLinks from '@/components/TierItemLinks.vue';
import { cn } from '@/helpers/cn';
import { SCORE_DEFINITIONS, getDynamicColor, getDynamicColorWithAlpha } from '@/helpers/scores';
import { findValidImageUrl } from '@/helpers/utils';
import type { BlogGameResult } from '@/server/routers/blog';

interface Props {
  items: (BlogGameResult & { score?: number })[];
  title?: string;
  emoji?: string;
  scoreColor?: string;
  compact?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '🏆 Top rankings',
  emoji: '',
  scoreColor: '#0a0',
  compact: false,
});

// Reactive state for toggling more
const showRunnerUpsState = ref(!props.compact);

// Get top 10 games (3 for podium + 7 for more)
const podiumGames = computed(() =>
  [props.items[1], props.items[0], props.items[2]].filter(Boolean),
);
const runnerUps = computed(() => props.items.slice(3, 10));

// Toggle more visibility
const toggleRunnerUps = () => {
  showRunnerUpsState.value = !showRunnerUpsState.value;
};

// Generate unique ID for this component instance
const componentId = computed(() => `ranking-box-${props.title.toLowerCase().replace(/\s+/g, '-')}`);

// Helper function to generate tooltip content for scores
const getScoreTooltip = (game: BlogGameResult & { score?: number }) => {
  if (!game.tierReview) return `Score: ${Math.round(game.score || 0)}/100`;

  const tierReview = game.tierReview;
  const finalScore = tierReview.finalScore || game.score || 0;

  // Build detailed score breakdown
  const scoreBreakdown = Object.values(SCORE_DEFINITIONS)
    .map(scoreDef => {
      const value = tierReview[scoreDef.key];
      return value !== null && value !== undefined ? `${scoreDef.label}: ${value}/100` : null;
    })
    .filter(Boolean)
    .join('\n');

  return scoreBreakdown
    ? `Final Score: ${Math.round(finalScore)}/100\n\n${scoreBreakdown}`
    : `Score: ${Math.round(finalScore)}/100`;
};

// Calculate average score for background styling
const averageScore = computed(() => {
  const validScores = props.items
    .filter(item => item.score && item.score > 0)
    .map(item => item.score!);
  if (validScores.length === 0) return 75; // Default fallback
  return validScores.reduce((sum, score) => sum + score, 0) / validScores.length;
});

// Generate beautiful background based on scoreColor and average score
const backgroundStyle = computed(() => {
  const score = averageScore.value;
  const baseColor = props.scoreColor;

  // Create a subtle gradient using the scoreColor with different opacities
  const primaryColor = getDynamicColorWithAlpha(baseColor, score, 0.15);
  const secondaryColor = getDynamicColorWithAlpha(baseColor, score, 0.08);
  const accentColor = getDynamicColorWithAlpha(baseColor, score, 0.05);

  return {
    background: `
      linear-gradient(135deg,
        ${primaryColor} 0%,
        ${secondaryColor} 50%,
        ${accentColor} 100%
      ),
      linear-gradient(45deg,
        rgba(31, 41, 55, 0.9) 0%,
        rgba(17, 24, 39, 0.8) 100%
      )
    `,
    borderColor: getDynamicColorWithAlpha(baseColor, score, 0.3),
    boxShadow: `
      0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px ${getDynamicColorWithAlpha(baseColor, score, 0.1)},
      inset 0 1px 0 0 ${getDynamicColorWithAlpha(baseColor, score, 0.1)}
    `,
  };
});
</script>

<template>
  <div
    class="ranking-box rounded-lg p-6 backdrop-blur-sm transition-all duration-300"
    :style="backgroundStyle"
  >
    <div class="mb-6 flex items-center justify-between">
      <h3 class="title text-theme-secondary flex-1 text-xl font-semibold">
        <span v-if="emoji" class="mr-2">{{ emoji }}</span>
        {{ title }}
      </h3>
      <button
        v-if="compact"
        @click="toggleRunnerUps"
        class="toggle-runners text-theme-primary hover:text-theme-secondary cursor-pointer text-sm font-medium transition-colors"
      >
        <span v-if="!showRunnerUpsState">More ↓</span>
        <span v-else>Less ↑</span>
      </button>
      <a
        v-else
        href="/blog/rankings/2025"
        class="text-theme-primary hover:text-theme-secondary text-sm font-medium transition-colors"
      >
        See more →
      </a>
    </div>

    <!-- Olympic Podium -->
    <div class="podium-container">
      <div class="podium-grid">
        <div
          v-for="(game, index) in podiumGames"
          :key="game?.steamId || index"
          :class="
            cn([
              'podium-item relative flex flex-col items-center transition-transform hover:scale-105',
              // Desktop positioning and sizing
              {
                'md:mt-0 md:w-48': index === 1, // 1st place
                'md:mt-4 md:w-24': index !== 1, // 2nd and 3rd place
              },
              // Mobile grid positioning
              {
                'podium-first': index === 1, // 1st place - full width row
                'podium-second': index === 0, // 2nd place - left half
                'podium-third': index === 2, // 3rd place - right half
              },
            ])
          "
          :style="{ animationDelay: index === 1 ? '0s' : index === 0 ? '0.2s' : '0.4s' }"
        >
          <template v-if="game">
            <!-- Game Cover -->
            <div class="game-cover group relative mb-2">
              <img
                :src="findValidImageUrl(game)"
                :alt="game.gameName"
                :class="
                  cn(
                    'rounded-lg object-cover shadow-lg transition-transform group-hover:scale-110',
                    // Desktop sizing
                    index === 1 ? ['md:w-48'] : ['md:w-36'],
                    // Mobile sizing
                    index === 1 ? ['w-40'] : ['w-28'],
                  )
                "
                loading="lazy"
              />
              <!-- Medal overlay -->
              <div
                :class="
                  cn(
                    'absolute -top-1 -right-1 animate-bounce',
                    compact ? 'text-lg' : 'text-2xl md:text-2xl',
                    index !== 1 ? 'text-xl md:text-2xl' : '',
                  )
                "
              >
                {{ index === 1 ? '🥇' : index === 0 ? '🥈' : '🥉' }}
              </div>
              <!-- TierItemLinks overlay -->
              <div class="absolute top-1 left-1">
                <TierItemLinks :tierItem="{ gameSteam: game, targetUrl: game.gameUrl }" />
              </div>
            </div>

            <!-- Game Name -->
            <div class="mb-2 text-center">
              <h4
                :class="
                  cn(
                    'line-clamp-2 font-semibold text-gray-200',
                    // Desktop sizing
                    index === 1 ? ['md:max-w-48 md:text-sm'] : ['md:max-w-24 md:text-sm'],
                    // Mobile sizing
                    index === 1 ? ['text-sm'] : ['text-xs'],
                  )
                "
              >
                {{ game.gameName }}
              </h4>
            </div>

            <!-- Score -->
            <div
              v-if="game.score"
              :class="
                cn(
                  'score-badge mb-3 cursor-help rounded-full font-bold shadow-lg',
                  // Desktop sizing
                  index === 1 ? ['md:px-3 md:py-1 md:text-sm'] : ['md:px-3 md:py-1 md:text-sm'],
                  // Mobile sizing
                  index === 1 ? ['px-3 py-1 text-sm'] : ['px-2 py-1 text-xs'],
                )
              "
              :style="`background-color: ${getDynamicColor(scoreColor, game.score)}; color: white;`"
              v-tooltip.top="{
                value: getScoreTooltip(game),
                pt: {
                  text: 'font-medium bg-black/90 text-sm whitespace-pre-line',
                },
              }"
            >
              {{ Math.round(game.score) }}
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- more List -->
    <div v-if="runnerUps.length > 0" v-show="showRunnerUpsState" :id="componentId" class="more">
      <h4 class="mb-4 text-center text-lg font-semibold text-gray-300">🎖️ Runner-ups</h4>
      <div class="space-y-2">
        <div
          v-for="(game, index) in runnerUps"
          :key="game.steamId"
          class="runner-up-item relative flex items-center gap-3 rounded-lg bg-gray-800/50 p-2 transition-colors hover:bg-gray-700/50 sm:gap-4 sm:p-3"
        >
          <!-- Place Number -->
          <div
            class="place-number flex h-8 w-8 items-center justify-center rounded-full bg-gray-600 text-sm font-bold text-white"
          >
            {{ index + 4 }}
          </div>

          <!-- Game Cover (smaller) -->
          <div class="relative">
            <img
              :src="findValidImageUrl(game)"
              :alt="game.gameName"
              class="h-12 w-12 rounded object-cover shadow-md"
              loading="lazy"
            />
            <!-- TierItemLinks overlay -->
            <div class="absolute -top-1 -right-1">
              <TierItemLinks :tierItem="{ gameSteam: game, targetUrl: game.gameUrl }" />
            </div>
          </div>

          <!-- Game Info -->
          <div class="min-w-0 flex-1">
            <h5 class="max-w-[40vw] truncate text-sm font-medium text-gray-200">
              {{ game.gameName }}
            </h5>
          </div>

          <!-- Score -->
          <div
            v-if="game.score"
            class="score-badge cursor-help rounded px-2 py-1 text-xs font-bold"
            :style="`background-color: ${getDynamicColor(scoreColor, game.score)}; color: white;`"
            v-tooltip.top="{
              value: getScoreTooltip(game),
              pt: {
                text: 'font-medium bg-black/90 text-sm whitespace-pre-line',
              },
            }"
          >
            {{ Math.round(game.score) }}
          </div>
        </div>
      </div>
    </div>

    <!-- No games message -->
    <div v-if="podiumGames.length === 0" class="py-8 text-center text-gray-400">
      <div class="mb-2 text-4xl">🎮</div>
      <p>No scored games found yet!</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ranking-box {
  position: relative;
  overflow: hidden;

  // Subtle animated background shimmer
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.03), transparent);
    animation: shimmer 3s infinite;
    pointer-events: none;
  }

  // Enhanced hover effect
  &:hover {
    transform: translateY(-2px);

    &::before {
      animation-duration: 1.5s;
    }
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.podium-item {
  animation: podiumRise 0.8s ease-out;
}

@keyframes podiumRise {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-badge {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.runner-up-item {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Podium Grid */
.podium-grid {
  /* Mobile layout: CSS Grid */
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 1rem;
  justify-items: center;
  align-items: start;

  /* Desktop layout: Flexbox */
  @media (min-width: 768px) {
    display: flex;
    align-items: start;
    justify-content: center;
    gap: 1rem;
  }
}

/* Mobile grid positioning */
.podium-first {
  grid-column: 1 / -1; /* Full width */
  grid-row: 1;
}

.podium-second {
  grid-column: 1;
  grid-row: 2;
}

.podium-third {
  grid-column: 2;
  grid-row: 2;
}

/* Desktop: Reset grid positioning */
@media (min-width: 768px) {
  .podium-first,
  .podium-second,
  .podium-third {
    grid-column: unset;
    grid-row: unset;
  }
}
</style>
