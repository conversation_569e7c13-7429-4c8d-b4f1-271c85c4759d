import type { APIContext, GetStaticPathsResult } from 'astro';
import { sql } from 'kysely';
import { jsonArrayFrom } from 'kysely/helpers/postgres';

import { Resvg } from '@resvg/resvg-js';

import type { ComputedPropertiesOfGameSteam } from '@/database/gameSteam';
import { type KyselyDatabase, kysely } from '@/database/kysely';
import { getSupabaseServerClient } from '@/database/supabase.server';
import { applyComputedTierSet } from '@/database/tierSet';
import { getTierImageStr } from '@/helpers/images/tier-image-utils';

export const prerender = false;

const query = kysely.selectFrom('tierSet as ts').select(['ts.slug', 'ts.label']);

export async function getStaticPaths(): Promise<GetStaticPathsResult> {
  const tierSets = await query.execute();

  return tierSets.map(tierSet => ({
    params: { tierSetSlug: tierSet.slug! },
  }));
}

type Props = {};

type Params = {
  tierSetSlug: string;
};

export async function GET({ params, request, cookies }: APIContext<Props, Params>) {
  const filePath = `og-image/tiers/${params.tierSetSlug}.png`;

  const supabase = getSupabaseServerClient(request.headers.get('Cookie'), cookies);
  // const {
  //   data: { publicUrl },
  // } = supabase.storage.from('images').getPublicUrl(filePath);

  // try {
  //   const responseBefore = await fetch(publicUrl, { method: 'HEAD' });
  //   if (responseBefore.status == 200) {
  //     return redirect(publicUrl);
  //   }
  // } catch {
  //   // Proceed to create image
  // }

  const result = await query
    .where('ts.slug', '=', params.tierSetSlug)
    .innerJoin('tierLane as tl', 'tl.tierSetSlug', 'ts.slug')
    .select(eb =>
      jsonArrayFrom(
        eb
          .selectFrom('tierItem as ti')
          .whereRef('ti.tierLaneSlug', '=', 'tl.slug')
          .innerJoin('gameSteam as gs', 'gs.steamId', 'ti.gameSteamId')
          .where(eb2 =>
            eb2.or([
              eb2('gs.hasImageLibraryPoster', '=', true),
              eb2('gs.hasImageHeroCapsule', '=', true),
            ]),
          )
          .selectAll('ti')
          .select(sql<KyselyDatabase['gameSteam']>`to_json(gs.*)`.as('gameSteam'))
          .limit(50),
      ).as('tierItems'),
    )
    .executeTakeFirst();

  if (!result) {
    return new Response(null, {
      status: 404,
      statusText: 'Not found',
    });
  }

  const tierSet = applyComputedTierSet(result);

  const svg = await getTierImageStr({
    title: tierSet.label ?? '',
    images: tierSet.tierItems.map(
      item => (item.gameSteam as unknown as ComputedPropertiesOfGameSteam)?.imageFinalTall!,
    ),
  });
  const png = new Resvg(svg).render().asPng();

  supabase.storage.from('images').upload(filePath, png);

  return new Response(png, {
    headers: { 'Content-Type': 'image/png' },
  });
}
