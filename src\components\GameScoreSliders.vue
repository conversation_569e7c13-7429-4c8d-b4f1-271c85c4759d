<script setup lang="ts">
import * as _ from 'lodash-es';

import { cn } from '@/helpers/cn';
import { SCORE_DEFINITIONS, getDynamicColor, getDynamicColorWithAlpha } from '@/helpers/scores';
import { trpc } from '@/services/trpc';
import type { GameSteamWithRating } from '@/stores/item';

const props = defineProps<{
  game: GameSteamWithRating;
}>();

const emit = defineEmits<{
  (e: 'scoreUpdated', scoreType: string, score: number): void;
}>();

// Reactive scores object
const scores = reactive<Record<string, number>>({
  ..._.mapValues(SCORE_DEFINITIONS, () => 0),
});

// Track which sliders are being dragged
const isDragging = reactive<Record<string, boolean>>({
  ..._.mapValues(SCORE_DEFINITIONS, () => false),
});

// Track shake animations
const shakeAnimations = reactive<Record<string, boolean>>({
  ..._.mapValues(SCORE_DEFINITIONS, () => false),
});

// Track feedback states
const feedbackStates = reactive<Record<string, 'success' | 'error' | 'loading' | null>>({
  ..._.mapValues(SCORE_DEFINITIONS, () => null),
});

// Store original values for rollback on error
const originalScores = reactive<Record<string, number>>({
  ..._.mapValues(SCORE_DEFINITIONS, () => 0),
});

// Initialize scores from game's tierReview
watchEffect(() => {
  if (props.game?.tierReview) {
    Object.values(SCORE_DEFINITIONS).forEach(({ key }) => {
      scores[key] = props.game.tierReview?.[key] ?? 0;
      originalScores[key] = props.game.tierReview?.[key] ?? 0;
      isDragging[key] = false;
      shakeAnimations[key] = false;
    });
  } else {
    // Default scores if no tierReview exists
    Object.values(SCORE_DEFINITIONS).forEach(({ key }) => {
      scores[key] = 0;
      originalScores[key] = 0;
      isDragging[key] = false;
      shakeAnimations[key] = false;
    });
  }
});

// Update function (called when mouse is released)
const updateScore = async (scoreType: string, score: number) => {
  try {
    feedbackStates[scoreType] = 'loading';
    await trpc.tierReview.updateScore.mutate({
      gameSteamId: props.game.steamId,
      scoreType: scoreType as any,
      score: score,
    });

    // Show success feedback
    feedbackStates[scoreType] = 'success';
    setTimeout(() => {
      feedbackStates[scoreType] = null;
    }, 1000);

    // Update original score for future rollbacks
    originalScores[scoreType] = score;
    emit('scoreUpdated', scoreType, score);
  } catch (error) {
    console.error('Failed to update score:', error);

    // Show error feedback
    feedbackStates[scoreType] = 'error';
    setTimeout(() => {
      feedbackStates[scoreType] = null;
    }, 1000);

    // Rollback to original value
    scores[scoreType] = originalScores[scoreType];
  }
};

// Haptic feedback (during drag)
const triggerHapticFeedback = (intensity: number) => {
  if ('vibrate' in navigator) {
    // Scale vibration duration based on score (higher score = longer vibration)
    const duration = Math.floor((intensity / 100) * 200) + 50; // 50-250ms
    navigator.vibrate(duration);
  }
};

// Shake animation (on release)
const triggerShakeAnimation = (scoreType: string, intensity: number) => {
  shakeAnimations[scoreType] = true;
  setTimeout(
    () => {
      shakeAnimations[scoreType] = false;
    },
    300 + (intensity / 100) * 200,
  ); // 300-500ms shake duration
};

// Handle slider events
const handleSliderInput = (scoreType: string, value: number) => {
  scores[scoreType] = value;
  triggerHapticFeedback(value);
};

const handleSliderMouseDown = (scoreType: string) => {
  isDragging[scoreType] = true;
};

const handleSliderMouseUp = (scoreType: string) => {
  if (isDragging[scoreType]) {
    isDragging[scoreType] = false;
    // Trigger shake animation on release
    triggerShakeAnimation(scoreType, scores[scoreType]);
    // Update score via API
    updateScore(scoreType, scores[scoreType]);
  }
};

// Calculate thumb position for icon alignment
const getThumbPosition = (score: number) => {
  // Slider thumb is 28px wide, so we need to account for that
  // The thumb center moves from 14px to (100% - 14px)
  const thumbWidth = 28;
  const percentage = score / 100;
  return `calc(${percentage * 100}% - ${percentage * thumbWidth}px + ${thumbWidth / 2}px)`;
};
</script>

<template>
  <div :class="cn('grid grow grid-cols-5 gap-1 p-2')">
    <div
      v-for="scoreDef in SCORE_DEFINITIONS"
      :key="scoreDef.key"
      :class="
        cn(
          'flex items-center gap-2 transition-transform duration-200',
          shakeAnimations[scoreDef.key] ? 'animate-shake' : '',
        )
      "
      v-tooltip.top="{
        value: `${scoreDef.label} (${scores[scoreDef.key]})`,
        pt: {
          text: 'font-black! bg-black/50!',
        },
      }"
    >
      <!-- Label -->
      <div :class="cn('flex w-5 items-center gap-2 font-mono text-xs font-medium text-gray-300')">
        {{ scoreDef.abbr }}
      </div>

      <!-- Slider with icon thumb -->
      <div :class="cn('relative flex-1')">
        <input
          :value="scores[scoreDef.key]"
          type="range"
          min="0"
          max="100"
          step="1"
          :class="
            cn(
              'h-3 w-full cursor-pointer appearance-none rounded-lg',
              'bg-gray-700 transition-colors duration-200 outline-none',
              'icon-slider-thumb',
              scores[scoreDef.key] === 0 ? 'opacity-10' : '',
            )
          "
          :style="{
            background: `linear-gradient(to right,
            ${getDynamicColorWithAlpha(scoreDef.color, scores[scoreDef.key], 0.4)}  0%,
            ${getDynamicColor(scoreDef.color, scores[scoreDef.key])}                ${scores[scoreDef.key]}%,
            #374151                                                                 ${scores[scoreDef.key]}%,
            #374151                                                                 100%)`,
            '--thumb-color': getDynamicColor(scoreDef.color, scores[scoreDef.key]),
          }"
          @input="
            e => handleSliderInput(scoreDef.key, Number((e.target as HTMLInputElement).value))
          "
          @mousedown="handleSliderMouseDown(scoreDef.key)"
          @mouseup="handleSliderMouseUp(scoreDef.key)"
          @touchstart="handleSliderMouseDown(scoreDef.key)"
          @touchend="handleSliderMouseUp(scoreDef.key)"
        />

        <!-- Icon overlay for thumb -->
        <div
          :class="cn('pointer-events-none absolute top-1/2 -translate-x-1/2 -translate-y-1/2')"
          :style="{
            left: getThumbPosition(scores[scoreDef.key]),
            color:
              feedbackStates[scoreDef.key] === 'success'
                ? '#10B981'
                : feedbackStates[scoreDef.key] === 'error'
                  ? '#EF4444'
                  : '#1f2937',
            fontSize: '14px',
            textShadow: '0 1px 2px rgba(0,0,0,0.5)',
          }"
        >
          <div v-if="feedbackStates[scoreDef.key] === 'loading'">⌛</div>
          <span v-else-if="feedbackStates[scoreDef.key] === 'success'">🎉</span>
          <span v-else-if="feedbackStates[scoreDef.key] === 'error'">❌</span>
          <span v-else>{{ scoreDef.icon }}</span>
        </div>
      </div>

      <!-- Score value -->
      <span
        :class="
          cn('w-8 rounded-full px-1 text-center text-xs font-bold transition-colors duration-200')
        "
        :style="{
          backgroundColor: getDynamicColorWithAlpha(scoreDef.color, scores[scoreDef.key], 0.2),
          color: getDynamicColor(scoreDef.color, scores[scoreDef.key]),
          border: `1px solid ${getDynamicColorWithAlpha(scoreDef.color, scores[scoreDef.key], 0.4)}`,
        }"
      >
        {{ scores[scoreDef.key] }}
      </span>
    </div>
  </div>
</template>

<style scoped>
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.icon-slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: var(--thumb-color, #8b5cf6);
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.icon-slider-thumb::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.icon-slider-thumb::-moz-range-thumb {
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: var(--thumb-color, #8b5cf6);
  cursor: pointer;
  border: 2px solid #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.icon-slider-thumb::-moz-range-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}
</style>
