---
import TagLink from '@/components/TagLink.astro';
import type { TierSetForBlog } from '@/server/routers/blog';
import { Image } from 'astro:assets';

interface Props {
  tierSet: TierSetForBlog;
}

const { tierSet } = Astro.props;

const targetUrl = `/blog/tiers/${tierSet.slug}`;
---

<div class="flex">
  <a class="min-w-[128px] rounded-xs" href={targetUrl}>
    <Image
      src={`/og-image/tiers/${tierSet.slug}.png`}
      alt={`${tierSet.label}`}
      width={128}
      height={64}
      class="object-contain"
    />
  </a>
  <div class="ml-4 flex max-w-full flex-col gap-1">
    <a href={targetUrl} class="cactus-link rounded-xs" rel="prefetch">
      <span class="font-bold">{tierSet.label}</span>
      <span class="ml-1">{tierSet.description}</span>
    </a>

    <div class="tags flex flex-wrap gap-1">
      {
        tierSet.tierLanes!.map(lane => (
          <div>
            <TagLink tierLane={lane} tierSetSlug={tierSet.slug} />
          </div>
        ))
      }
    </div>
  </div>
</div>
