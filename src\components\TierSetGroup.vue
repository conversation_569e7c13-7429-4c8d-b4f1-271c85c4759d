<script setup lang="ts">
import { useToast } from 'primevue/usetoast';

import { watchDebounced } from '@vueuse/core';

import TierSet from '@/components/TierSet.vue';
import type { TierSetForMainPage } from '@/database/tierSet';
import { trpc } from '@/services/trpc';
import { pinia, useItemStore } from '@/stores/item';

import GameInfo from './GameInfo.vue';
import TierFilters from './TierFilters.vue';

const props = defineProps({
  tierSets: {
    type: Object as () => TierSetForMainPage[],
    required: true,
  },
});

const toast = useToast();

const itemStore = useItemStore(pinia);
const { schema, hasDemo, hasReview, fSearchText } = storeToRefs(itemStore);
const lastFiltered = ref(new Date());
const isLoading = ref(false);

schema.value = props.tierSets;

watchDebounced(
  [fSearchText, hasDemo, hasReview],
  async () => {
    isLoading.value = true;
    try {
      const tierSets = await trpc.tierSet.retrieveMany.query({
        slugs: props.tierSets.map(set => set.slug),
        tierLanes: {
          includeCount: true,
        },
        tierItems: {
          search: fSearchText.value,
          hasDemo: hasDemo.value,
          hasReview: hasReview.value,
          skip: 0,
          take: 40,
        },
      });
      schema.value = tierSets as unknown as TierSetForMainPage[];
      lastFiltered.value = new Date();
    } catch (err) {
      console.error(err);
      toast.add({
        severity: 'error',
        summary: "Can't search",
        detail: 'Maybe the tier is too big :(',
        life: 20000,
      });
    }
    isLoading.value = false;
  },
  { debounce: 1_000 },
);
</script>

<template>
  <PrimeToast />
  <TierFilters class="mb-2" :disabled="isLoading" />

  <div class="flex flex-col-reverse">
    <div v-for="(tierSet, i) in tierSets">
      <TierSet :index="i" :key="lastFiltered.valueOf()" />
    </div>
  </div>

  <GameInfo></GameInfo>
</template>
