<script setup lang="ts">
import type { MarkdownHeading } from 'astro';

import { sanitize } from '@/helpers/utils';

const headings = ref<MarkdownHeading[]>([]);

onMounted(() => {
  const elements = [...document.querySelector('#content')!.querySelectorAll(`h1,h2,h3,h4,h5,h6`)];
  const hs = elements.map(el => {
    const heading: MarkdownHeading = {
      depth: +el.tagName.at(-1)!,
      slug: el.id,
      text: sanitize(el.innerHTML, { ALLOWED_TAGS: [] }),
    };
    return heading;
  });
  headings.value = hs;
});
</script>

<template>
  <div>
    <h2 class="font-semibold">Table of Contents</h2>
    <ul class="text-xs">
      <template v-for="{ depth, slug, text } in headings">
        <li class="hover:text-primary line-clamp-2">
          <a
            :class="`block ${
              [
                'mt-3',
                'mt-3',
                'mt-2 ps-3 text-[0.8rem]',
                'mt-2 ps-4 text-[0.75rem]',
                'mt-2 ps-5 text-[0.7rem]',
                'mt-2 ps-6 text-[0.65rem]',
              ][depth]
            }`"
            :href="`#${slug}`"
            :aria-label="`Scroll to section: ${text}`"
          >
            <span class="font-bold">#</span> {{ text }}
          </a>
        </li>
      </template>
    </ul>
  </div>
</template>
