import type { APIRoute } from 'astro';

import rss from '@astrojs/rss';

import type { ComputedPropertiesOfGameSteam } from '@/database/gameSteam';
import { CONST } from '@/helpers/constants';
import { filterOnlyReviewed, sanitize, toMarkDown } from '@/helpers/utils';
import { getServerTrpcCaller } from '@/server/caller';
import { SERVER_ENV } from '@/server/env';

export const prerender = true;

export const GET: APIRoute = async ctx => {
  const { caller } = await getServerTrpcCaller(ctx, { prerender });

  const { reviewedGames: games } = await caller.blog.fetchGames();

  return rss({
    title: `${CONST.title} - ${CONST.titleExt}`,
    description: CONST.description,
    site: SERVER_ENV.SITE ?? '',
    items: games.map(g => {
      const review = g.tierReview;

      return {
        /** Link to item */
        link: `blog/${g.slug}`,
        /** Full content of the item. Should be valid HTML */
        // content?: string | undefined;
        /** Title of item */
        title: review.reviewTitle || g.gameName || '',
        /** Publication date of item */
        pubDate: (review.publishDate as Date) || new Date(review.createdAt),
        /** Item description */
        description: sanitize(
          [
            review.reviewContent ? toMarkDown(review.reviewContent) : '',
            `<img src=${g?.screenshots?.[0]?.path_full} alt="Screenshot" style="max-height: 200px;" />`,
            (g as ComputedPropertiesOfGameSteam).movies
              .slice(0, 1)
              .flatMap(
                m =>
                  `<video src=${m.teaser} controls loop style="width: 100%; height: 100%"></video>`,
              ),
          ].join(''),
        ),
        /** Append some other XML-valid data to this item */
        // customData?: z.infer<typeof rssSchema>['customData'];
        /** Whether draft or not */
        // draft?: z.infer<typeof rssSchema>['draft'];
        /** Categories or tags related to the item */
        categories: [
          'Game Demo Review',
          'Video Games',
          'Gaming News',
          'Game Reviews',
          'Demo Impressions',
          'Thai Gaming Community',
          'Thai Game Reviews',
          'Thai Language',
          'Thai Gaming News',
          ...(g.tierItems?.flatMap(ti => ti.tierLane?.label!).filter(Boolean) ?? []),
        ],
        /** The item author's email address */
        author: CONST.author,
        /** A URL of a page for comments related to the item */
        // commentsUrl?: z.infer<typeof rssSchema>['commentsUrl'];
        /** The RSS channel that the item came from */
        source: {
          title: `${CONST.title} - ${CONST.titleExt}`,
          url: CONST.siteUrl,
        },
        /** A media object that belongs to the item */
        // enclosure?: z.infer<typeof rssSchema>['enclosure'];
      };
    }),
  });
};
