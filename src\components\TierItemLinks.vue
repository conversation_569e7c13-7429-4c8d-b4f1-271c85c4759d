<script setup lang="ts">
import { Icon } from '@iconify/vue';

import { cn } from '@/helpers/cn';

const props = defineProps({
  tierItem: {
    type: Object as () => {
      targetUrl?: string;
      gameSteam?: {
        isReviewed?: boolean;
        slug?: string;
        platformWin?: boolean | null;
        platformMac?: boolean | null;
        platformLinux?: boolean | null;
      } | null;
    },
    required: true,
  },
});

// Add this computed property to get platform icons
const platformIcons = computed(() => {
  const icons = [];

  if (props.tierItem.gameSteam?.platformWin) {
    icons.push({ icon: 'mdi:microsoft-windows', tooltip: 'Windows', color: '#00adef' });
  }

  if (props.tierItem.gameSteam?.platformMac) {
    icons.push({ icon: 'mdi:apple', tooltip: 'macOS', color: '#ff6b6b' });
  }

  if (props.tierItem.gameSteam?.platformLinux) {
    icons.push({ icon: 'mdi:linux', tooltip: 'Linux', color: '#f5bd0c' });
  }

  if (icons.length === 0 && props.tierItem.targetUrl) {
    icons.push({ icon: 'mdi:steam', tooltip: 'Steam', color: '#fff' });
  }

  return icons;
});
</script>

<template>
  <!-- Platform icons -->
  <a
    v-for="platform in platformIcons"
    :key="platform.icon"
    :href="tierItem.targetUrl"
    target="_blank"
    @click.stop=""
    v-tooltip.top="platform.tooltip"
    :class="cn('flex items-center justify-center rounded-full', 'transition-all hover:scale-110')"
  >
    <Icon :icon="platform.icon" :style="{ color: platform.color }" class="text-lg" :inline="true" />
  </a>
  <a
    v-if="tierItem.gameSteam?.isReviewed"
    :href="`/blog/${tierItem.gameSteam.slug}`"
    target="_blank"
    @click.stop=""
  >
    <Icon icon="mdi:note-text"></Icon>
  </a>
</template>
