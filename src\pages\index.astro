---
import ReviewBlogPost from '@/components/ReviewBlogPost.astro';
import RecentActivityBox from '@/components/RecentActivityBox.vue';
import RankingBox from '@/components/RankingBox.vue';
import SocialList from '@/components/SocialList.astro';
import Layout from '@/layouts/Layout.astro';

import { getServerTrpcCaller } from '@/server/caller';

export const prerender = true;

const { caller } = await getServerTrpcCaller(Astro, { prerender });

const { reviewedGames } = await caller.blog.fetchGames();

const TIER_1_COUNT = 4;

const highlighedGames = reviewedGames.slice(0, TIER_1_COUNT);

const topGames = reviewedGames
  .map(game => ({ ...game, score: game.tierReview?.finalScore }))
  .filter(game => typeof game.score === 'number')
  .toSorted((a, b) => b.score! - a.score!);
---

<Layout title="Home" pagefindIgnore>
  <section class="px-2">
    <h1 class="title">Welcome</h1>
    <p class="mt-4">
      เว็บนี้ผมเอาไว้รวบรวม Demo เกมที่ผมเคยเล่นมา เพื่อที่จะได้ดูเอง และให้คนอื่นได้ดูง่ายๆ<br />
      เวลาเล่น Demo ใหม่ จะได้ไม่ต้องรอทำคลิป เพราะทำคลิปมันใช้เวลาเหลือเกิ๊นน<br />
      ซึ่งรีวิวทั้งหมดมาจากความเห็นของผมเอง ที่มีความลำเอียง เรื่องมาก<br />
      เหมือนมนุษย์ทั่วไป อาจจะไม่ตรงกับความเห็นของมนุษย์คนไหนเลย หรือคนส่วนใหญ่<br />
      ดังนั้นดูเอาความบันเทิง แล้วลองเล่นเองนะครับ Have fun :)
    </p>
    <SocialList class="mt-4" />
  </section>
  <section aria-label="Recent games" class="mt-4">
    <h2 class="title mb-4 text-xl">Played Recently</h2>
    <div class="grid grid-cols-4 gap-4">
      {
        highlighedGames.map(item => (
          <div class="col-span-4 aspect-16/9 md:col-span-2">
            <ReviewBlogPost game={item} ensureImage />
          </div>
        ))
      }
    </div>
  </section>
  <section aria-label="Past games" class="mt-4">
    <RecentActivityBox items={reviewedGames} client:load offset={4} />
  </section>
  <section aria-label="Top rankings" class="mt-4">
    <RankingBox items={topGames} />
  </section>
</Layout>
