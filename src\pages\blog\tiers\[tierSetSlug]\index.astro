---
import TagLink from '@/components/TagLink.astro';
import { Content as HypnessContent } from '@/content/hypeness.md';
import { Content as SuckzContent } from '@/content/suckz.md';
import Layout from '@/layouts/Layout.astro';
import { getServerTrpcCaller } from '@/server/caller';

export const prerender = true;

export async function getStaticPaths() {
  const { caller } = await getServerTrpcCaller(Astro, { prerender });
  const { tierSets } = await caller.blog.fetchTiers();

  return tierSets.map(tierSet => {
    return {
      params: { tierSetSlug: tierSet.slug },
      props: { tierSet },
    };
  });
}

const tierSet = Astro.props.tierSet;
---

<Layout
  title={`All ${tierSet.label}`}
  description={`All ${tierSet.label} from this website`}
  pagefindIgnore
>
  <h1 class="title mb-6">{tierSet.label}</h1>
  <ul class="space-y-4">
    {
      tierSet.tierLanes!.map(tierLane => (
        <li class="flex items-center gap-x-2">
          <TagLink tierSetSlug={tierSet.slug} tierLane={tierLane} header>
            {tierLane.label}
          </TagLink>
          <span class="inline-block">
            -
            <span title="Reviewed" data-tooltip-placement="top" class="text-green-600">
              {tierLane.tierItems!.filter(r => r.gameSteam?.isReviewed).length} R
            </span>
            <span title="Pending" data-tooltip-placement="top" class="text-yellow-600">
              {
                tierLane.tierItems!.filter(r => r.gameSteam?.isPlayed && !r.gameSteam?.isReviewed)
                  .length
              }{' '}
              P
            </span>
            <span title="Skipped" data-tooltip-placement="top" class="text-slate-400">
              {tierLane.tierItems!.filter(r => r.gameSteam?.isSkipped).length} S
            </span>
          </span>
        </li>
      ))
    }
  </ul>
  <div class="prose prose-sm prose-cactus mt-3">
    {tierSet.slug === 'hypeness' && <HypnessContent />}
    {tierSet.slug === 'suckz' && <SuckzContent />}
  </div>
</Layout>
