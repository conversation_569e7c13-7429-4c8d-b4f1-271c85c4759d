import { format } from 'date-fns';
import { th } from 'date-fns/locale';
import type { Selectable } from 'kysely';

import { type ComputedPropertiesOfGameSteam, applyComputedGameSteam } from './gameSteam';
import type { KyselyDatabase } from './kysely';
import { type ComputedPropertiesOfTierLane, applyComputedTierLane } from './tierLane';

export type TierItemForMainPage = ReturnType<
  typeof applyComputedTierItem<Selectable<KyselyDatabase['tierItem']>>
>;

export type ComputedPropertiesOfTierItem = {
  fullTitle?: string;
  publishDateFormatted?: string;
  tierSetSlug?: string;
  targetUrl?: string;
  gameSteam?: ComputedPropertiesOfGameSteam;
  tierLane?: Selectable<KyselyDatabase['tierLane']>;
  fromLane?: ComputedPropertiesOfTierLane;
  score?: number;
  createdAt?: Date;
  updatedAt?: Date;
  publishDate?: Date;
  isReviewed?: boolean;
};

export function applyComputedTierItem<T extends object>(input: T) {
  const applied: Omit<T, 'createdAt' | 'updatedAt'> & ComputedPropertiesOfTierItem = input as any;

  const tierItem = input as Selectable<KyselyDatabase['tierItem']>;

  if (applied.gameSteam) {
    applied.gameSteam = applyComputedGameSteam(applied.gameSteam);
  }
  if (applied.tierLane) {
    applied.tierSetSlug = applied.tierLane.tierSetSlug;
  }
  if (applied.fromLane) {
    applied.fromLane = applyComputedTierLane(applied.fromLane);
  }

  const gameSteam = applied.gameSteam as Selectable<KyselyDatabase['gameSteam']> &
    ComputedPropertiesOfGameSteam;

  applied.fullTitle = (() => {
    const reviewTitleParts: string[] = [];
    if (gameSteam?.gameName) {
      reviewTitleParts.push(gameSteam.gameName);
    }
    if (tierItem.reviewTitle) {
      reviewTitleParts.push(tierItem.reviewTitle);
    }
    return reviewTitleParts.join(' ');
  })();

  if ('publishDate' in tierItem) {
    if (typeof applied.publishDate === 'string') {
      applied.publishDate = new Date(applied.publishDate);
    }
    applied.publishDateFormatted = (() => {
      return tierItem.publishDate
        ? format(tierItem.publishDate, 'dd MMMM yyyy', { locale: th })
        : '';
    })();
  }

  if ('createdAt' in tierItem) {
    if (typeof applied.createdAt === 'string') {
      applied.createdAt = new Date(applied.createdAt);
    }
  }

  applied.targetUrl = (() => {
    if (applied.targetUrl) {
      return applied.targetUrl;
    }

    if (gameSteam?.gameUrl) {
      return gameSteam.gameUrl;
    }

    if (gameSteam?.steamId) {
      return `https://store.steampowered.com/app/${gameSteam?.steamId}`;
    }

    return '';
  })();

  applied.score = (() => {
    let score = applied.tierLane?.score ?? 0;
    return score;
  })();

  applied.isReviewed = (() => {
    return applied.isReviewed ?? !!tierItem.reviewTitle;
  })();

  return applied;
}
